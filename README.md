This is a new [**React Native**](https://reactnative.dev) project, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).

# Getting Started

> **Note**: Make sure you have completed the [Set Up Your Environment](https://reactnative.dev/docs/set-up-your-environment) guide before proceeding.

## Step 1: Start Metro

First, you will need to run **Metro**, the JavaScript build tool for React Native.

To start the Metro dev server, run the following command from the root of your React Native project:

```sh
# Using npm
npm start

# OR using Yarn
yarn start
```

## Step 2: Build and run your app

With Metro running, open a new terminal window/pane from the root of your React Native project, and use one of the following commands to build and run your Android or iOS app:

### Android

```sh
# Using npm
npm run android

# OR using Yarn
yarn android
```

### iOS

For iOS, remember to install CocoaPods dependencies (this only needs to be run on first clone or after updating native deps).

The first time you create a new project, run the Ruby bundler to install CocoaPods itself:

```sh
bundle install
```

Then, and every time you update your native dependencies, run:

```sh
bundle exec pod install
```

For more information, please visit [CocoaPods Getting Started guide](https://guides.cocoapods.org/using/getting-started.html).

```sh
# Using npm
npm run ios

# OR using Yarn
yarn ios
```

If everything is set up correctly, you should see your new app running in the Android Emulator, iOS Simulator, or your connected device.

This is one way to run your app — you can also build it directly from Android Studio or Xcode.

## Step 3: Modify your app

Now that you have successfully run the app, let's make changes!

Open `App.tsx` in your text editor of choice and make some changes. When you save, your app will automatically update and reflect these changes — this is powered by [Fast Refresh](https://reactnative.dev/docs/fast-refresh).

When you want to forcefully reload, for example to reset the state of your app, you can perform a full reload:

- **Android**: Press the <kbd>R</kbd> key twice or select **"Reload"** from the **Dev Menu**, accessed via <kbd>Ctrl</kbd> + <kbd>M</kbd> (Windows/Linux) or <kbd>Cmd ⌘</kbd> + <kbd>M</kbd> (macOS).
- **iOS**: Press <kbd>R</kbd> in iOS Simulator.

## Congratulations! :tada:

You've successfully run and modified your React Native App. :partying_face:

### Now what?

- If you want to add this new React Native code to an existing application, check out the [Integration guide](https://reactnative.dev/docs/integration-with-existing-apps).
- If you're curious to learn more about React Native, check out the [docs](https://reactnative.dev/docs/getting-started).

# Troubleshooting

If you're having issues getting the above steps to work, see the [Troubleshooting](https://reactnative.dev/docs/troubleshooting) page.

# Learn More

To learn more about React Native, take a look at the following resources:

- [React Native Website](https://reactnative.dev) - learn more about React Native.
- [Getting Started](https://reactnative.dev/docs/environment-setup) - an **overview** of React Native and how setup your environment.
- [Learn the Basics](https://reactnative.dev/docs/getting-started) - a **guided tour** of the React Native **basics**.
- [Blog](https://reactnative.dev/blog) - read the latest official React Native **Blog** posts.
- [`@facebook/react-native`](https://github.com/facebook/react-native) - the Open Source; GitHub **repository** for React Native.


## Project-specific run scripts

Besides the standard React Native commands above, this project defines handy scripts:

### Android
- Debug (Lower flavor):
  - Yarn: `yarn android:lower`
  - npm: `npm run android:lower`
- Debug (Production flavor):
  - Yarn: `yarn android:production`
  - npm: `npm run android:production`
- Release builds (assemble only):
  - Lower: `yarn android:lowerRelease` / `npm run android:lowerRelease`
  - Production: `yarn android:productionRelease` / `npm run android:productionRelease`

### iOS
- Default: `yarn ios` / `npm run ios`
- One-step install pods & launch: `yarn ios:launch` / `npm run ios:launch`
- Lower scheme: `yarn ios:lower` / `npm run ios:lower`
- Production scheme: `yarn ios:production` / `npm run ios:production`
- Explicit simulator: `yarn ios:sim` / `npm run ios:sim`
- Open Simulator app: `yarn ios:open-sim` / `npm run ios:open-sim`
- Boot iPhone 15 Pro simulator: `yarn ios:boot:15pro` / `npm run ios:boot:15pro`
- Clean one-step launch (reset cache, install pods, run on iPhone 15 Pro): `yarn ios:launch:clean` / `npm run ios:launch:clean`

These map to Xcode schemes already present in the repo and should match the Android product flavors.


## Compatibility warning: macOS 15 + React Native 0.80.x

If you are on macOS 15 (Sequoia) on Apple Silicon and this project uses React Native 0.80.x, there is a known incompatibility related to toolchain/patching (gflags/glog) that prevents reliable iOS builds and runs.

What we’ve done in this repo:
- Added an automated preflight check that runs before iOS commands and stops with an actionable message if you’re on macOS 15+ with RN 0.80.x.

Your options:
- Upgrade React Native to 0.81+ or 0.82+
- Use a different machine running macOS 14 or earlier
- Wait for upstream fixes or consider using Expo

To bypass the check temporarily (at your own risk):
```sh
OVERRIDE_MACOS15_RN80=1 yarn ios
# or any other iOS script, e.g.
OVERRIDE_MACOS15_RN80=1 yarn ios:launch
```

This guard is there to save time by failing fast with clear remediation steps. If you proceed via the override, builds may still fail on macOS 15 with RN 0.80.x.
