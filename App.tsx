/**
 * Subway React Native App with MVC Architecture
 * 
 * Architecture:
 * - Model: AppModel (handles state and data)
 * - View: Screen components (SplashScreen, LandingPage)
 * - Controller: AppController (handles user interactions and business logic)
 *
 * @format
 */

import React, { useEffect, useState } from 'react';
import { StatusBar, View, Platform } from 'react-native';
import { AppNavigator } from './src';
import { DebugMenuHandler } from './src/components/DebugGestureHandler';
import { environmentManager } from './src/config/EnvironmentConfig';

// Initialize localization and environment
import { i18n } from './src/locales/i18n';

function App(): React.JSX.Element {
  const [isI18nReady, setIsI18nReady] = useState(false);
  const [isEnvironmentReady, setIsEnvironmentReady] = useState(false);

  useEffect(() => {
    // Initialize environment manager
    const initializeEnvironment = async () => {
      await environmentManager.initialize();
      setIsEnvironmentReady(true);
    };
    
    initializeEnvironment();
  }, []);

  useEffect(() => {
    // Wait for i18n to be ready - faster check
    const checkI18nReady = () => {
      if (i18n.isInitialized) {
        setIsI18nReady(true);
      } else {
        // If not ready, wait a shorter time and check again
        setTimeout(checkI18nReady, 10); // Much faster check
      }
    };
    
    checkI18nReady();
  }, []);

  if (!isI18nReady || !isEnvironmentReady) {
    // Show same background as splash screen while initializing
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#0066cc' }}>
        {/* No loading text - just the same background as splash */}
      </View>
    );
  }

  return (
    <>
      <StatusBar 
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
        backgroundColor={Platform.OS === 'android' ? '#0066cc' : 'white'}
        translucent={false} 
      />
      <DebugMenuHandler>
        <AppNavigator />
      </DebugMenuHandler>
    </>
  );
}

export default App;
