# Subway React Native App - Build Configuration Summary

## Overview
Successfully implemented Android and iOS build targets for "Lower" and "Production" environments with proper environment configuration and build restrictions.

## ✅ Completed Tasks

### 1. Android Configuration
- **Removed** `applicationIdSuffix` from lower flavor (both flavors use same app ID)
- **Renamed** "debug" flavor to "lower" 
- **Created** product flavors:
  - `lower`: For UAT/QE environments
  - `production`: For production environment
- **Build variants available**:
  - `assembleLowerRelease` - Lower environment release build
  - `assembleProductionRelease` - Production environment release build

### 2. iOS Configuration  
- **Created** custom schemes with environment variables:
  - `SubwayRNApp-Lower`: Sets `ENVIRONMENT=lower` 
  - `SubwayRNApp-Production`: Sets `ENVIRONMENT=production`
- **Scheme configurations**:
  - Lower: Uses Debug configuration with lower environment
  - Production: Uses Release configuration with production environment

### 3. Build Configuration System
- **Updated** `scripts/build-config.js`:
  - Renamed "debug" to "lower" 
  - Changed `IS_DEBUG_BUILD` to `IS_LOWER_BUILD`
  - Updated validation to accept "lower" and "production"
- **Auto-generates** `src/config/BuildConfig.ts` with:
  - `ENVIRONMENT`: 'lower' or 'production'
  - `IS_PRODUCTION_BUILD`: boolean
  - `TIMESTAMP`: build timestamp

### 4. Environment Management
- **Updated** `src/config/EnvironmentConfig.ts`:
  - Uses generated build config to determine environment
  - **Lower builds**: Allow switching between UAT and QE environments
  - **Production builds**: Only allow production environment
  - **Environment switching**: Only enabled in lower builds + development mode

### 5. Build Scripts (package.json)
- **Android**:
  - `android:lower`: Run lower build on device
  - `android:production`: Run production build on device  
  - `android:build-lower`: Build lower APK
  - `android:build-production`: Build production APK
- **iOS**:
  - `ios:lower`: Run lower build with SubwayRNApp-Lower scheme
  - `ios:production`: Run production build with SubwayRNApp-Production scheme
  - `ios:build-lower`: Build lower IPA with Xcode
  - `ios:build-production`: Build production IPA with Xcode

### 6. App Naming
- **Lower builds**: "Subway Lower" (Android), same app ID as production
- **Production builds**: "Subway"

## 🚀 Build Commands

### Android
```bash
# Build Lower Release APK
npm run android:build-lower

# Build Production Release APK  
npm run android:build-production

# Run on device
npm run android:lower        # Lower environment
npm run android:production   # Production environment
```

### iOS
```bash
# Build Lower Release IPA
npm run ios:build-lower

# Build Production Release IPA
npm run ios:build-production

# Run on device/simulator
npm run ios:lower       # Lower environment
npm run ios:production  # Production environment
```

## 🔒 Environment Restrictions

| Build Type | Available Environments | Environment Switching |
|------------|----------------------|---------------------|
| Lower | UAT, QE | ✅ Enabled (dev mode only) |
| Production | Production only | ❌ Disabled |

## 📱 App Identification

| Build Type | Android App Name | iOS App Name | App ID |
|------------|------------------|--------------|---------|
| Lower | "Subway Lower" | "Subway Lower" | Same as production |
| Production | "Subway" | "Subway" | Same as production |

## 📁 Generated Files

- `/src/config/BuildConfig.ts` - Auto-generated, ignored in git
- `/android/app/build/outputs/apk/lower/release/app-lower-release.apk`
- `/android/app/build/outputs/apk/production/release/app-production-release.apk`

## ✅ Verification

1. **Android Lower Build**: ✅ Successfully builds `app-lower-release.apk`
2. **iOS Schemes**: ✅ `SubwayRNApp-Lower` and `SubwayRNApp-Production` available
3. **Environment Config**: ✅ Properly restricts environments based on build type
4. **Build Scripts**: ✅ All npm scripts working correctly

## 🎯 Key Benefits

1. **Environment Isolation**: Production builds cannot access lower environments
2. **Simplified App ID**: No suffix needed, both builds can coexist through different distribution methods
3. **Proper Naming**: Clear distinction between Lower and Production builds
4. **Automated Configuration**: Build config automatically generated based on target
5. **Cross-Platform Consistency**: Same environment logic works on both Android and iOS

---

**Status**: ✅ **COMPLETE** - All build targets configured and tested successfully.
