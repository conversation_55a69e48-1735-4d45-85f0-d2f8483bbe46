# Android Environment Configuration Summary

This document summarizes the Android multi-environment setup for the Subway React Native app.

## Overview

The Android app now supports two product flavors, each pointing to a specific environment:

- **Lower Flavor** (`com.subwayrnapp.lower`) → **Lower Environment**
- **Production Flavor** (`com.subwayrnapp`) → **Prod Environment**

## Configuration Details

### Android Build Configuration

**File:** `android/app/build.gradle`

```gradle
productFlavors {
    lower {
        dimension "environment"
        applicationIdSuffix ".lower"
        buildConfigField "String", "ENVIRONMENT", '"lower"'
        buildConfigField "boolean", "IS_PRODUCTION_BUILD", "false"
        resValue "string", "app_name", "Subway Lower"
    }
    production {
        dimension "environment"
        buildConfigField "String", "ENVIRONMENT", '"production"'
        buildConfigField "boolean", "IS_PRODUCTION_BUILD", "true"
        resValue "string", "app_name", "Subway"
    }
}
```

### Environment Mappings

| Android Flavor | App Name | Bundle ID | Environment | API Base URL |
|----------------|----------|-----------|-------------|--------------|
| lower | Subway Lower | com.subwayrnapp.lower | lower | https://api-lower.subway.com/ |
| production | Subway | com.subwayrnapp | prod | https://api-prod.subway.com/ |

### Build Scripts

**File:** `package.json`

```json
{
  "scripts": {
    "android:lower": "cd android && ./gradlew app:installLowerDebug && cd .. && react-native start",
    "android:production": "cd android && ./gradlew app:installProductionDebug && cd .. && react-native start"
  }
}
```

### Native Module Bridge

A custom native module (`BuildConfigModule`) exposes Android BuildConfig values to React Native:

**Files:**
- `android/app/src/main/java/com/subwayrnapp/BuildConfigModule.kt`
- `android/app/src/main/java/com/subwayrnapp/BuildConfigPackage.kt`
- `src/config/NativeBuildConfig.ts`

### Environment Configuration

**File:** `src/config/EnvironmentConfig.ts`

The environment manager automatically detects the current build flavor and selects the appropriate environment:

- Lower builds → Lower environment (non-production, allows environment switching in debug mode)
- Production builds → Prod environment (production, no environment switching)

### Environment Files

- `src/config/environments/lower.ts` - Lower environment configuration
- `src/config/environments/prod.ts` - Production environment configuration  
- `src/config/environments/cfa-uat.ts` - UAT environment (available in lower builds)
- `src/config/environments/cfa-qe.ts` - QE environment (available in lower builds)

## Usage

### Building and Installing

```bash
# Build and install lower flavor
npm run android:lower

# Build and install production flavor  
npm run android:production
```

### Launching Apps

```bash
# Launch lower flavor
adb shell am start -n com.subwayrnapp.lower/com.subwayrnapp.MainActivity

# Launch production flavor
adb shell am start -n com.subwayrnapp/com.subwayrnapp.MainActivity
```

## Side-by-Side Installation

Both flavors can be installed simultaneously on the same device because they have different bundle IDs:
- Lower: `com.subwayrnapp.lower`
- Production: `com.subwayrnapp`

## Debug Features

- Lower builds show environment debug information and allow environment switching in debug mode
- Production builds are locked to the prod environment with no debug features
- Both flavors display their configuration via the `EnvironmentDebug` component (can be removed for production)

## Verification

Both flavors have been successfully built, installed, and tested to ensure they point to their respective environments as required.
