package com.subwayrnapp

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.Promise

class BuildConfigModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "BuildConfigModule"
    }

    override fun getConstants(): MutableMap<String, Any> {
        val constants = HashMap<String, Any>()
        constants["ENVIRONMENT"] = BuildConfig.ENVIRONMENT
        constants["IS_PRODUCTION_BUILD"] = BuildConfig.IS_PRODUCTION_BUILD
        return constants
    }
}
