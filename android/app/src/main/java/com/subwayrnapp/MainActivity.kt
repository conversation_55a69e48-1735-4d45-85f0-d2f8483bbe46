package com.subwayrnapp

import android.os.Bundle
import android.content.Intent
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.facebook.react.ReactApplication
import com.facebook.react.bridge.ReactContext
import com.facebook.react.ReactInstanceEventListener
import com.facebook.react.bridge.UiThreadUtil
import com.facebook.react.devsupport.interfaces.DevSupportManager

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "SubwayRNApp"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    
    if (intent?.extras?.getBoolean("debug", false) == true) {
      // Enable debugging
      val app = application as ReactApplication
      val reactInstanceManager = app.reactNativeHost.reactInstanceManager
      val devManager = reactInstanceManager.devSupportManager
      
      UiThreadUtil.runOnUiThread {
        // Enable dev support
        devManager.setDevSupportEnabled(true)
        
        // Enable remote debugging
        devManager.devSettings.isRemoteDebuggingEnabled = true
        
        // Enable debugging bridge
        devManager.handleReloadJS()
        
        // Set debug server host to localhost:8081
        devManager.setDebugServerHost("localhost:8081")
        
        // Show dev menu after setup
        devManager.showDevOptionsDialog()
      }
    }
  }
}
