# Troubleshooting Guide

## Common Issues and Solutions

### 1. "SubwayRNApp has not been registered" Error

This error occurs when there's a mismatch between the app name in different configuration files.

**Solution:**
- Ensure `app.json` has `"name": "<PERSON><PERSON>pp"`
- Check that `App.tsx` properly exports the App component
- Restart Metro bundler with cache reset: `npx react-native start --reset-cache`

### 2. Build Errors with Dependencies

If you encounter build errors related to react-native-safe-area-context or similar libraries:

**Solution:**
- Use React Native's built-in `SafeAreaView` instead of third-party libraries
- Clean the build cache:
  ```bash
  # Android
  cd android && ./gradlew clean
  
  # iOS
  cd ios && rm -rf build && pod install
  ```

### 3. Metro Port Already in Use

**Error:** `EADDRINUSE: address already in use :::8081`

**Solution:**
```bash
# Kill existing processes on port 8081
lsof -ti:8081 | xargs kill -9

# Restart Metro
npx react-native start --reset-cache
```

### 4. Android Build Issues

**Common fixes:**
- Clean the project: `cd android && ./gradlew clean`
- Update build tools version in `android/app/build.gradle`
- Ensure Android SDK and build tools are properly installed

### 5. Navigation Issues

If navigation doesn't work properly:
- Ensure all screens are properly imported in `AppNavigator.tsx`
- Check that navigation props are passed correctly to screens
- Verify that the navigation container wraps all navigation components

## Development Commands

```bash
# Start Metro bundler
npx react-native start

# Run on Android
npx react-native run-android

# Run on iOS
npx react-native run-ios

# Clean and restart
npx react-native start --reset-cache

# Android clean build
cd android && ./gradlew clean && cd ..

# iOS clean build
cd ios && rm -rf build && pod install && cd ..
```

## Architecture Verification

To verify the MVC architecture is working correctly:

1. **Model Test:** Check that `AppModel` state changes trigger UI updates
2. **View Test:** Ensure screens render properly and handle user interactions
3. **Controller Test:** Verify that business logic flows through controllers

## Performance Tips

1. Use React Native's built-in components when possible
2. Minimize third-party dependencies to reduce build complexity
3. Use TypeScript for better error catching
4. Implement proper state management patterns

## File Structure Verification

Ensure the following structure exists:
```
src/
├── models/AppModel.ts
├── views/
│   ├── SplashScreen.tsx
│   ├── LandingPage.tsx
│   └── HomeScreen.tsx
├── controllers/AppController.ts
├── navigation/AppNavigator.tsx
├── styles/styles.ts
└── index.ts
```


### 6. iOS Simulator never launches (Apple Silicon Macs)

Symptoms:
- Build appears to succeed but the iOS Simulator never opens, or the app never installs/launches on the simulator.

Likely cause:
- The simulator architecture (arm64) is excluded in build settings. On Apple Silicon, iOS Simulator uses arm64. Excluding it prevents launch.

Status in this repo:
- The Podfile has been updated to NOT exclude arm64 for iOS Simulator builds.

What to do locally:
1) Install pods and clean caches
```bash
cd ios && pod install --repo-update
cd ..
# Optional: Clean Xcode DerivedData (helps when switching configs)
rm -rf ~/Library/Developer/Xcode/DerivedData
```

2) Restart Metro with cache reset
```bash
npx react-native start --reset-cache
```

3) Run the app on Simulator
```bash
# Default scheme
yarn ios
# Or choose a specific scheme
yarn ios:lower
# Or pick a specific device explicitly
npx react-native run-ios --simulator "iPhone 15 Pro"
```

4) If using Xcode directly
- Open ios/SubwayRNApp.xcworkspace
- Select a Simulator (e.g., iPhone 15 Pro) and the desired Scheme (SubwayRNApp, SubwayRNApp-Lower, or SubwayRNApp-Production)
- Product > Run

If it still doesn’t launch:
- Ensure no other Metro server is running (kill processes on 8081)
- From Xcode, do Product > Clean Build Folder and try again
- Re-run step (1) to re-install pods


## macOS 15 (Sequoia) + React Native 0.80.x Compatibility

There is a known incompatibility when building iOS on macOS 15 (Apple Silicon) with React Native 0.80.x related to the toolchain/patching (gflags/glog). This often prevents reliable builds or simulator runs.

What this repo does for you:
- A preflight check runs before iOS commands and blocks with a clear message if RN 0.80.x is detected on macOS 15+.

Recommended options:
1) Upgrade React Native to 0.81+ or 0.82+
2) Use a development machine running macOS 14 or earlier
3) Wait for upstream fixes or consider using Expo

Temporary bypass (at your own risk):
```bash
OVERRIDE_MACOS15_RN80=1 yarn ios
# or e.g.
OVERRIDE_MACOS15_RN80=1 yarn ios:launch
```

Note: Even with the override, builds may still fail due to the upstream incompatibility.
