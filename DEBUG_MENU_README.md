# Environment Switching & Debug Menu

This app includes a comprehensive environment switching system and debug menu for development and testing purposes.

## Features

### Environment Management
- **Production**: Uses production API endpoints
- **CFA-UAT**: Uses UAT testing environment
- **CFA-QE**: Uses QE testing environment

### Debug Menu (Non-Production Only)
The debug menu is only available in non-production environments and provides:

- **Build Information**: Version, build number, and platform details
- **Environment Switching**: Switch between different API environments
- **Cache Management**: Clear all cached data
- **Current Environment Status**: Shows which environment is currently active

## How to Access Debug Menu

### Development Builds
In development builds (non-production), you can access the debug menu using:

#### Method 1: Swipe Gesture (Improved)
- **Gesture**: Swipe left from the right edge of the screen
- **Implementation**: Uses both PanResponder and react-native-gesture-handler for better reliability
- **Trigger Area**: Right 50px of the screen
- **Minimum Distance**: 30px horizontal swipe to the left
- **Note**: If gesture isn't working, try the backup button

#### Method 2: Backup Debug Button
- **Location**: Tools icon (🛠️) button in the top-right corner
- **Purpose**: Backup access method for debug drawer
- **Design**: Light circular button with tools emoji

### Gesture Details
- **Edge Detection**: Touch must start within 50 pixels of the right screen edge
- **Swipe Distance**: Must swipe at least 30 pixels to the left, OR
- **Swipe Velocity**: Swipe with sufficient speed (velocity < -200)
- **Direction**: Only left swipes are detected (right-to-left)
- **Horizontal Swipe**: Must be more horizontal than vertical movement

### Debug Drawer Features

#### Build Information
- **Version**: Shows app version from package.json
- **Build**: Shows build number (timestamp-based in development)
- **Platform**: Shows iOS or Android

#### Environment Switching
- View current environment and its API base URL
- Switch between available environments:
  - **Production**: `https://api.subway.com/digital`
  - **CFA-UAT**: `https://cfa-uat-api.test.subway.com`
  - **CFA-QE**: `https://cfa-qe-api.test.subway.com`
- App will restart automatically after environment change

#### Cache Management
- Clear all cached landing page data
- Useful for testing fresh API responses

## Environment Configuration

Environment configurations are now split into separate files for better organization:

### File Structure
```
src/config/
├── EnvironmentConfig.ts          # Main environment manager
└── environments/
    ├── index.ts                  # Environment exports
    ├── production.ts             # Production config
    ├── cfa-uat.ts               # CFA UAT config
    └── cfa-qe.ts                # CFA QE config
```

### API Base URLs
```typescript
// Production Environment
{
  apiBaseUrl: 'https://api.subway.com/',
  mediaBaseUrl: 'https://media.subway.com',
}

// CFA UAT Environment  
{
  apiBaseUrl: 'https://cfa-uat-api.test.subway.com',
  mediaBaseUrl: 'https://stg-media.test.subway.com',
}

// CFA QE Environment
{
  apiBaseUrl: 'https://cfa-qe-api.test.subway.com',
  mediaBaseUrl: 'https://stg-media.test.subway.com',
}
```

### Landing Page URLs
- **Production**: `https://media.subway.com/digital/Mobile/GuestUser/GuestCheckoutLandingLoyalty{suffix}.json`
- **Lower Environments**: `https://stg-media.test.subway.com/digital/Mobile/GuestUser/GuestCheckoutLandingLoyalty{suffix}.json`

## Technical Implementation

### Key Components
- `EnvironmentManager`: Manages environment configuration and switching
- `DebugDrawer`: The debug menu UI component
- `DebugMenuHandler`: Wrapper component that adds debug functionality
- `BuildInfoService`: Extracts version and build information

### Environment Persistence
Environment preferences are stored in AsyncStorage and persist across app restarts.

### Production Safety
- Debug menu is completely hidden in production builds
- Environment switching is disabled in production
- Only production endpoints are used in production builds

## Development Usage

### Testing Different Environments
1. Open the debug menu using swipe gesture or the backup debug button
2. Tap on the desired environment in the "Switch Environment" section
3. Confirm the switch - the app will restart automatically
4. The new environment will be used for all API calls

### Troubleshooting Gesture Issues
If the swipe gesture isn't working:
1. Check console logs for gesture debug information
2. Ensure gesture starts from the right edge (within 50px)
3. Swipe horizontally to the left (minimum 30px)
4. Use the tools icon (🛠️) button as backup

### Clearing Cache
1. Open the debug menu
2. Scroll to the bottom and tap "Clear Cache"
3. Confirm the action
4. All cached landing page data will be cleared

### Checking Build Information
1. Open the debug menu
2. View the "Build Information" section for:
   - Current app version
   - Build number
   - Platform (iOS/Android)
   - Current environment details

This system ensures that developers and QA teams can easily switch between different environments for testing while maintaining production security and performance.
