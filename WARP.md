# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview
Phoenix is a React Native mobile application with multiple build variants (lower/production) for both iOS and Android platforms. The project uses TypeScript and implements Firebase Analytics.

## Development Environment Commands

### Metro Development Server
```sh
yarn start  # Start Metro bundler
```

### iOS Development
```sh
# First-time setup and after native dependency changes
bundle install  # Install Ruby dependencies
bundle exec pod install  # Install CocoaPods dependencies

# Development build
yarn ios  # Build and run on iOS simulator
```

### Android Development
```sh
# Development builds
yarn android  # Default development build
yarn android:lower  # Lower environment build
yarn android:production  # Production environment build

# Release builds
yarn android:lowerRelease  # Build lower environment release APK
yarn android:productionRelease  # Build production environment release APK
```

### Testing and Linting
```sh
yarn test  # Run Jest tests
yarn lint  # Run ESLint
```

## Project Architecture

### Key Dependencies
- `@react-native-firebase/app` & `analytics` - Firebase integration and analytics
- `i18next` & `react-i18next` - Internationalization
- `@react-native-async-storage/async-storage` - Persistent storage
- `react-native-fast-image` - Optimized image loading
- `react-native-device-info` - Device information access
- `lottie-react-native` - Animation support

### Build Variants
The application supports multiple build variants:
- Lower environment (development/testing)
- Production environment

This setup allows for different configurations and endpoints based on the build variant.

### Development Requirements
- Node.js >= 18.0.0
- Ruby dependencies (for iOS development)
- CocoaPods (for iOS development)
- Android Studio (for Android development)
- Xcode (for iOS development)

## Troubleshooting

### iOS Build Issues
1. If pod install fails:
   ```sh
   bundle install  # Ensure Ruby dependencies are up to date
   bundle exec pod install --repo-update  # Force update of CocoaPods repos
   ```

2. For build errors after React Native upgrade:
   ```sh
   cd ios
   bundle exec pod deintegrate
   bundle exec pod install
   ```

### Android Build Issues
1. For Gradle sync failures:
   - Ensure Android Studio is updated
   - Run `cd android && ./gradlew clean`
   - Delete `android/app/build` directory

2. For "SDK location not found":
   - Create `android/local.properties` with your SDK path:
     ```
     sdk.dir=/Users/<USER>/Library/Android/sdk
     ```

### Development Workflow
- Enable Fast Refresh in dev menu for quick iterations
- Use `yarn start --reset-cache` to clear Metro bundler cache
- For iOS simulator: Press R to reload
- For Android emulator: Press R twice to reload
