require_relative '../node_modules/react-native/scripts/react_native_pods'

# Override the minimum iOS version to match your requirements
platform :ios, min_ios_version_supported

prepare_react_native_project!

use_frameworks! :linkage => :static

target 'SubwayRNApp' do
  # Force all pods to be static frameworks
  config = use_native_modules!
  
  if !config[:use_frameworks]
    config[:use_frameworks] = { :linkage => :static }
  end
  config = use_native_modules!

  # All React Native pods are managed by react_native_pods.rb
  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => false,
    :fabric_enabled => false,
    # An absolute path to your application root.
    :app_path => "#{Dir.pwd}/.."
  )

  # Pods for your additional dependencies
  # Firebase dependencies with modular headers
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true


  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    
    installer.pods_project.build_configurations.each do |config|
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
    end
    
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = min_ios_version_supported
        if target.name == 'React-Core' || target.name == 'React-Core.common-CoreModulesHeaders'
          config.build_settings['HEADER_SEARCH_PATHS'] = ' /ReactCommon /ReactCommon/react/nativemodule/core/platform/ios /boost /DoubleConversion /RCT-Folly /boost /Headers/Private/React-Core'
        end
        # Fix for Firebase modulemap issue
        if target.name == 'FirebaseCoreInternal'
          config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
        end
        # Fix for Lottie modulemap issues
        if target.name == 'lottie-ios' || target.name == 'lottie-react-native'
          config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
        end
      end
    end
  end
end
