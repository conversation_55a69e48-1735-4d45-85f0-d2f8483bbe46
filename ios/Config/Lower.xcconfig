//
// Lower Configuration
// Sets the bundle ID suffix for lower builds and enables debug mode
//

// Import debug config but override critical debug settings
#include "Pods/Target Support Files/Pods-SubwayRNApp/Pods-SubwayRNApp.debug.xcconfig"

BUNDLE_ID_SUFFIX = .lower

// Force DEBUG mode - completely override any NDEBUG settings from dependencies
GCC_PREPROCESSOR_DEFINITIONS = DEBUG=1 COCOAPODS=1 SD_WEBP=1

// Override OTHER_CFLAGS to remove any NDEBUG flags from dependencies
OTHER_CFLAGS = $(PODS_OTHER_CFLAGS) -DDEBUG=1 -UDNDEBUG

// Override OTHER_CPLUSPLUSFLAGS to remove NDEBUG and add debug flags
OTHER_CPLUSPLUSFLAGS = $(PODS_OTHER_CPLUSPLUSFLAGS) -DDEBUG=1 -UDNDEBUG

// Ensure Swift debug mode
SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG

// Other debug-related settings
DEBUG_INFORMATION_FORMAT = dwarf-with-dsym
ENABLE_NS_ASSERTIONS = YES
ENABLE_TESTABILITY = YES
GCC_OPTIMIZATION_LEVEL = 0
SWIFT_OPTIMIZATION_LEVEL = -Onone

//
//  Lower.xcconfig
//  SubwayRNApp
//
//  Created by David Haddad on 2024-07-08.
//

// Configuration settings file format documentation can be found at:
// https://help.apple.com/xcode/#/dev745c5c974

#include "Base.xcconfig"
// Include CocoaPods configuration for the Lower build
#include "Pods/Target Support Files/Pods-SubwayRNApp/Pods-SubwayRNApp-Lower.xcconfig"

// App Settings
PRODUCT_BUNDLE_IDENTIFIER = com.subwayrnapp.lower
PRODUCT_NAME = SubwayLower

// Custom Flags
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) __DEV__=1 NDEBUG= NDEBUG_THIS_IS_NOT_A_PRODUCTION_BUILD=1
// Force NDEBUG to be undefined to prevent production optimizations
GCC_PREPROCESSOR_DEFINITIONS[config=Lower] = $(inherited) NDEBUG=
SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Lower] = $(inherited) Lower

// This will undefine NDEBUG for the Lower configuration, ensuring it's not treated as a production build.
// It helps in keeping debugging features active.
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) NDEBUG=
