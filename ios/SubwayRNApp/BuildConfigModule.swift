//
//  BuildConfigModule.swift
//  SubwayRNApp
//
//  Created to provide build configuration information to React Native

import Foundation
import React

@objc(BuildConfigModule)
class BuildConfigModule: NSObject {
  
  @objc
  static func requiresMainQueueSetup() -> <PERSON><PERSON> {
    return false
  }
  
  @objc
  func constantsToExport() -> [AnyHashable : Any]! {
    // Determine bundle ID for all builds
    let bundleId = Bundle.main.bundleIdentifier ?? ""
    var environment: String
    var isProductionBuild: Bool

    // If the bundle ID ends with ".lower", always force non-production values
    if bundleId.hasSuffix(".lower") {
      isProductionBuild = false
      environment = "cfa-uat"
    } else {
      // For all other configurations, read values from Info.plist
      isProductionBuild = (Bundle.main.object(forInfoDictionaryKey: "IS_PRODUCTION_BUILD") as? String ?? "true") == "true"
      environment = Bundle.main.object(forInfoDictionaryKey: "ENVIRONMENT") as? String ?? "production"
    }

    // Additional safety check: prevent any accidental production on .lower bundle IDs
    if bundleId.hasSuffix(".lower") && isProductionBuild {
      // Should never happen; force values and log error
      isProductionBuild = false
      environment = "cfa-uat"
      assertionFailure("FATAL: A .lower bundle ID was incorrectly configured as a production build. Overriding to non-production.")
    }

    // Debug logging for build config
    print("BuildConfigModule: BUNDLE_ID = \(bundleId)")
    print("BuildConfigModule: IS_PRODUCTION_BUILD = \(isProductionBuild)")
    print("BuildConfigModule: ENVIRONMENT = \(environment)")

    return [
      "BUNDLE_ID": bundleId,
      "IS_PRODUCTION_BUILD": isProductionBuild,
      "ENVIRONMENT": environment
    ]
  }
}
