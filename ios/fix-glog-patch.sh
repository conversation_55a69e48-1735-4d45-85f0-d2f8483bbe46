#!/bin/bash

# Fix for glog patch compatibility with macOS 15+
# This script addresses the "patch -p1" compatibility issue

set -e

echo "🔧 Applying glog patch compatibility fix for macOS 15+..."

# Check if we're on macOS 15+
if [[ "$(sw_vers -productVersion)" == 15.* ]]; then
    echo "📱 Detected macOS 15+, applying compatibility fixes..."
    
    # Create a compatible patch command
    if command -v gpatch >/dev/null 2>&1; then
        echo "✅ Found gpatch, using it instead of patch"
        export PATCH_CMD="gpatch"
    else
        echo "⚠️  gpatch not found, trying to install it..."
        if command -v brew >/dev/null 2>&1; then
            brew install gnu-patch
            export PATCH_CMD="gpatch"
        else
            echo "❌ Could not install gpatch, trying alternative approach..."
            export PATCH_CMD="patch"
        fi
    fi
    
    # Export the patch command for use in pod installation
    export PATCH_CMD
fi

echo "✅ Glog patch compatibility fix applied"
