#!/bin/bash

# iOS Build Configuration Script
# Configures the iOS build for different environments (Lower or Production)

set -e

BUILD_TYPE="$1"

if [ -z "$BUILD_TYPE" ]; then
    echo "Usage: $0 [lower|production]"
    echo ""
    echo "Build types:"
    echo "  lower      - Builds with .lower bundle suffix (CFA-UAT, CFA-QE environments available)"
    echo "  production - Builds with standard bundle ID (Production environment only)"
    exit 1
fi

PROJECT_DIR="$(dirname "$0")"
PLIST_BUDDY="/usr/libexec/PlistBuddy"
INFO_PLIST="$PROJECT_DIR/SubwayRNApp/Info.plist"

case "$BUILD_TYPE" in
    "lower")
        echo "🔧 Configuring iOS build for LOWER environment..."
        
        # Copy Lower-specific Info.plist
        if [ -f "$PROJECT_DIR/SubwayRNApp/Info-Lower.plist" ]; then
            cp "$PROJECT_DIR/SubwayRNApp/Info-Lower.plist" "$INFO_PLIST"
            echo "✅ Using Info-Lower.plist"
        else
            echo "❌ Info-Lower.plist not found!"
            exit 1
        fi

        # Copy Lower-specific GoogleService-Info.plist
        if [ -f "$PROJECT_DIR/Config/GoogleService-Info-lower.plist" ]; then
            cp "$PROJECT_DIR/Config/GoogleService-Info-lower.plist" "$PROJECT_DIR/SubwayRNApp/GoogleService-Info.plist"
            echo "✅ Using GoogleService-Info-lower.plist"
        else
            echo "❌ GoogleService-Info-lower.plist not found!"
            exit 1
        fi
        
        echo "✅ iOS Lower build configured successfully"
        echo "   - Bundle ID will have .lower suffix"
        echo "   - Environment switching enabled (CFA-UAT, CFA-QE)"
        ;;
        
    "production")
        echo "🔧 Configuring iOS build for PRODUCTION environment..."
        
        # Copy Production-specific Info.plist
        if [ -f "$PROJECT_DIR/SubwayRNApp/Info-Production.plist" ]; then
            cp "$PROJECT_DIR/SubwayRNApp/Info-Production.plist" "$INFO_PLIST"
            echo "✅ Using Info-Production.plist"
        else
            echo "❌ Info-Production.plist not found!"
            exit 1
        fi

        # Copy Production-specific GoogleService-Info.plist
        if [ -f "$PROJECT_DIR/Config/GoogleService-Info-production.plist" ]; then
            cp "$PROJECT_DIR/Config/GoogleService-Info-production.plist" "$PROJECT_DIR/SubwayRNApp/GoogleService-Info.plist"
            echo "✅ Using GoogleService-Info-production.plist"
        else
            echo "❌ GoogleService-Info-production.plist not found!"
            exit 1
        fi
        
        echo "✅ iOS Production build configured successfully"
        echo "   - Standard bundle ID (no suffix)"
        echo "   - Production environment only"
        ;;
        
    *)
        echo "❌ Invalid build type: $BUILD_TYPE"
        echo "Valid options: lower, production"
        exit 1
        ;;
esac

echo ""
echo "🚀 Ready to build iOS app with $BUILD_TYPE configuration"
echo "   Use: npx react-native run-ios --scheme=SubwayRNApp-$(echo $BUILD_TYPE | sed 's/.*/\u&/')"
