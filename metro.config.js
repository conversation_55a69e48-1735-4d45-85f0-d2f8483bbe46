const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  // Additional settings for Bridgeless architecture
  resolver: {
    unstable_enablePackageExports: true,
    unstable_enableSymlinks: true,
  }
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
