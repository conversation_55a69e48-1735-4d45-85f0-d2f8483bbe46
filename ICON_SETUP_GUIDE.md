# 🚇 Subway App Icon Setup Guide

## Overview
The Subway React Native app now supports **dual app icons**:
- **Regular Icon**: Used for development/lower builds (from `subway-app-icon.png`)
- **Production Icon**: Used for production builds (from `subway-prod-app-icon.png`)

## 📱 Android Icon Configuration

### Lower Flavor (Development)
- **Location**: `android/app/src/main/res/mipmap-*/`
- **Icons**: Uses the regular `subway-app-icon.png`
- **App Name**: "Subway Lower"
- **Bundle ID**: `com.subwayrnapp.lower`

### Production Flavor (Production)
- **Location**: `android/app/src/production/res/mipmap-*/`
- **Icons**: Uses the production `subway-prod-app-icon.png`
- **App Name**: "Subway"
- **Bundle ID**: `com.subwayrnapp`

### Generated Icon Sizes
Both flavors include icons at 5 density levels:
- `mipmap-mdpi/` (48x48px)
- `mipmap-hdpi/` (72x72px)
- `mipmap-xhdpi/` (96x96px)
- `mipmap-xxhdpi/` (144x144px)
- `mipmap-xxxhdpi/` (192x192px)

Each density includes:
- `ic_launcher.png` (square icon)
- `ic_launcher_round.png` (circular icon for round launchers)

## 🍎 iOS Icon Configuration

### Lower Scheme (Development)
- **Asset Catalog**: `AppIcon.appiconset/`
- **Icons**: Uses the regular `subway-app-icon.png`
- **App Name**: "Subway"
- **Bundle ID**: `com.subwayrnapp.lower`

### Production Scheme (Production)
- **Asset Catalog**: `AppIcon-Production.appiconset/`
- **Icons**: Uses the production `subway-prod-app-icon.png`
- **App Name**: "Subway"
- **Bundle ID**: `com.subwayrnapp`

### Generated Icon Sizes
Both asset catalogs include icons at 9 sizes:
- `<EMAIL>` (40x40px)
- `<EMAIL>` (60x60px)
- `<EMAIL>` (58x58px)
- `<EMAIL>` (87x87px)
- `<EMAIL>` (80x80px)
- `<EMAIL>` (120x120px)
- `<EMAIL>` (120x120px)
- `<EMAIL>` (180x180px)
- `icon-1024.png` (1024x1024px) - App Store

## 🚀 Build Commands

### Development/Lower Builds
```bash
# Android Lower (debug)
npm run android:lower

# iOS Lower (debug)
npm run ios:lower

# Android Lower (release)
npm run android:build-lower

# iOS Lower (release)
npm run ios:build-lower
```

### Production Builds
```bash
# Android Production (debug)
npm run android:production

# iOS Production (debug)  
npm run ios:production

# Android Production (release)
npm run android:build-production

# iOS Production (release)
npm run ios:build-production
```

## 🔧 How It Works

### Android
- **Android Gradle Plugin** automatically selects icons based on the flavor:
  - `lower` flavor → `src/main/res/` (regular icons)
  - `production` flavor → `src/production/res/` (production icons)
- Each flavor has its own resource folder with the appropriate icons

### iOS
- **Environment Variables** in schemes control which icon asset is used:
  - `SubwayRNApp-Lower` scheme → `APPICON_NAME=AppIcon`
  - `SubwayRNApp-Production` scheme → `APPICON_NAME=AppIcon-Production`
- The build configuration uses `ASSETCATALOG_COMPILER_APPICON_NAME = "${APPICON_NAME}"`

## 📁 File Structure

```
├── src/assets/
│   ├── subway-app-icon.png          # Source: Regular icon
│   └── subway-prod-app-icon.png     # Source: Production icon
├── android/app/src/
│   ├── main/res/mipmap-*/           # Regular Android icons
│   └── production/res/mipmap-*/     # Production Android icons
├── ios/SubwayRNApp/Images.xcassets/
│   ├── AppIcon.appiconset/          # Regular iOS icons
│   └── AppIcon-Production.appiconset/ # Production iOS icons
└── scripts/
    ├── setup-app-icon.js            # Generate regular icons
    ├── setup-production-app-icon.js # Generate production icons
    └── verify-all-icons.js          # Verify all icons exist
```

## 🛠️ Regenerating Icons

### Regular Icons (Lower/Development)
```bash
node scripts/setup-app-icon.js
```

### Production Icons
```bash
node scripts/setup-production-app-icon.js
```

### Verification
```bash
node scripts/verify-all-icons.js
```

## ✅ Verification Checklist

- [ ] Regular Android icons exist in `android/app/src/main/res/mipmap-*/`
- [ ] Production Android icons exist in `android/app/src/production/res/mipmap-*/`
- [ ] Regular iOS icons exist in `ios/SubwayRNApp/Images.xcassets/AppIcon.appiconset/`
- [ ] Production iOS icons exist in `ios/SubwayRNApp/Images.xcassets/AppIcon-Production.appiconset/`
- [ ] Lower Android build shows "Subway Lower" app name
- [ ] Production Android build shows "Subway" app name
- [ ] Both iOS builds show "Subway" app name
- [ ] Lower builds use regular icon
- [ ] Production builds use production icon

## 🎯 Result

Users can now install both development and production versions of the app side-by-side, each with:
- **Distinct app icons** for easy identification
- **Appropriate app names** for clarity
- **Separate bundle IDs** to prevent conflicts
- **Environment-specific configurations**

The visual distinction between development and production builds helps prevent confusion during testing and deployment.
