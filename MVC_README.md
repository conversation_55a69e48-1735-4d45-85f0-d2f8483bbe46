# Subway React Native App - MVC Architecture

This React Native app implements the Model-View-Controller (MVC) architectural pattern for better code organization and maintainability.

## Architecture Overview

### Model (`src/models/`)
- **AppModel.ts**: Handles application state management, user data, and business logic
- Implements Observer pattern for state management
- Manages user authentication state
- Handles app initialization

### View (`src/views/`)
- **SplashScreen.tsx**: Initial loading screen with Subway branding
- **LandingPage.tsx**: Welcome screen with login functionality
- **HomeScreen.tsx**: Main app screen after authentication

### Controller (`src/controllers/`)
- **AppController.ts**: Coordinates between Model and View layers
- Handles user interactions and navigation
- Manages business logic flow
- Acts as intermediary between UI and data

## Project Structure

```
src/
├── models/           # Data models and business logic
│   └── AppModel.ts
├── views/            # UI components and screens
│   ├── SplashScreen.tsx
│   ├── LandingPage.tsx
│   └── HomeScreen.tsx
├── controllers/      # Business logic coordinators
│   └── AppController.ts
├── navigation/       # Navigation setup
│   └── AppNavigator.tsx
├── styles/          # Styling and themes
│   └── styles.ts
└── index.ts         # Main exports
```

## Features

### Splash Screen
- 3-second loading screen
- Subway branding
- Automatic transition to landing page
- Loading indicator

### Landing Page
- Welcome screen with Subway theming
- Login modal with email/password
- Guest mode option
- Feature highlights
- Responsive design

### Home Screen
- Personalized welcome message
- Menu grid with quick actions
- User state management
- Logout functionality

## Key Benefits of MVC Architecture

1. **Separation of Concerns**: Each layer has distinct responsibilities
2. **Maintainability**: Easier to modify and extend functionality
3. **Testability**: Components can be tested independently
4. **Reusability**: Controllers and models can be reused across views
5. **Scalability**: Easy to add new features and screens

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. For iOS:
   ```bash
   cd ios && pod install
   ```

3. Run the app:
   ```bash
   # iOS
   npm run ios
   
   # Android
   npm run android
   ```

## Dependencies

- React Native 0.80.1
- React Navigation v6
- TypeScript support

## Data Flow

1. **User Interaction** → View captures user input
2. **View** → Controller receives the interaction
3. **Controller** → Model updates application state
4. **Model** → Notifies observers of state changes
5. **View** → Updates UI based on new state

## State Management

The app uses a custom state management system with the Observer pattern:

- `AppModel` maintains application state
- Components subscribe to state changes
- Automatic UI updates when state changes
- Type-safe state management with TypeScript

## Navigation Flow

```
SplashScreen (3s) → LandingPage → HomeScreen
                        ↓
                   Login Modal
```

## Styling

- Centralized styling in `src/styles/styles.ts`
- Subway brand colors (green #009E43, yellow #FFBF00)
- Responsive design patterns
- Consistent theming across screens

## Future Enhancements

- Add more screens (Menu, Order, Profile)
- Implement real authentication
- Add data persistence
- Add animations and transitions
- Implement push notifications
