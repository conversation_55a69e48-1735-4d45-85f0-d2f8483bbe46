/**
 * Landing Page API Service
 * Handles fetching, caching, and language mapping for landing page configuration
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { LandingPageApiResponse, CachedLandingData } from '../models/LandingPageModels';
import { environmentManager } from '../config/EnvironmentConfig';

export class LandingPageApiService {
  private static instance: LandingPageApiService;
  private cacheKeyPrefix = 'landing_page_data_';

  private constructor() {}

  static getInstance(): LandingPageApiService {
    if (!LandingPageApiService.instance) {
      LandingPageApiService.instance = new LandingPageApiService();
    }
    return LandingPageApiService.instance;
  }

  /**
   * Maps our internal language codes to API format
   */
  private mapLanguageCode(language: string): string {
    const languageMap: { [key: string]: string } = {
      'en-US': '', // Default, no suffix
      'en-CA': '_en-ca',
      'fr-CA': '_fr-ca',
      'en-PR': '_en-pr',
      'es-US': '_es-us',
      'es-PR': '_es-pr',
    };

    return languageMap[language] || '';
  }

  /**
   * Builds the API URL for the given language
   */
  private buildApiUrl(language: string): string {
    const suffix = this.mapLanguageCode(language);
    const baseUrl = `${environmentManager.getMediaBaseUrl()}/digital/Mobile/GuestUser/GuestCheckoutLandingLoyalty`;
    return `${baseUrl}${suffix}.json`;
  }

  /**
   * Gets the cache key for the given language
   */
  private getCacheKey(language: string): string {
    return `${this.cacheKeyPrefix}${language}`;
  }

  /**
   * Fetches data from cache
   */
  private async getCachedData(language: string): Promise<CachedLandingData | null> {
    try {
      const cacheKey = this.getCacheKey(language);
      const cachedDataString = await AsyncStorage.getItem(cacheKey);
      
      if (cachedDataString) {
        const cachedData: CachedLandingData = JSON.parse(cachedDataString);
        return cachedData;
      }
      
      return null;
    } catch (error) {
      console.warn('Error reading cached landing page data:', error);
      return null;
    }
  }

  /**
   * Saves data to cache
   */
  private async saveCachedData(language: string, data: LandingPageApiResponse, lastModified: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(language);
      const cachedData: CachedLandingData = {
        data,
        lastModified,
        timestamp: Date.now(),
        language,
      };
      
      await AsyncStorage.setItem(cacheKey, JSON.stringify(cachedData));
    } catch (error) {
      console.warn('Error saving landing page data to cache:', error);
    }
  }

  /**
   * Checks if cached data is still valid by comparing Last-Modified headers
   */
  private async checkIfDataIsStale(language: string, cachedData: CachedLandingData): Promise<boolean> {
    try {
      const url = this.buildApiUrl(language);
      const response = await fetch(url, { method: 'HEAD' });
      
      if (response.ok) {
        const lastModified = response.headers.get('Last-Modified');
        
        if (lastModified && lastModified !== cachedData.lastModified) {
          return true; // Data is stale
        }
      }
      
      return false; // Data is still fresh
    } catch (error) {
      console.warn('Error checking if data is stale:', error);
      return false; // Assume data is fresh if we can't check
    }
  }

  /**
   * Fetches fresh data from the API
   */
  private async fetchFreshData(language: string): Promise<{ data: LandingPageApiResponse; lastModified: string }> {
    const url = this.buildApiUrl(language);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch landing page data: ${response.status} ${response.statusText}`);
    }

    const data: LandingPageApiResponse = await response.json();
    const lastModified = response.headers.get('Last-Modified') || new Date().toUTCString();

    return { data, lastModified };
  }

  /**
   * Public method to check if valid cached data exists
   * Returns the cached API response if available and not stale, null otherwise
   */
  async getValidCachedData(language: string): Promise<LandingPageApiResponse | null> {
    try {
      const cachedData = await this.getCachedData(language);
      
      if (cachedData) {
        // Check if cached data is stale
        const isStale = await this.checkIfDataIsStale(language, cachedData);
        
        if (!isStale) {
          return cachedData.data;
        }
      }
      
      return null;
    } catch (error) {
      console.warn('Error checking cached data:', error);
      return null;
    }
  }

  /**
   * Main method to get landing page data
   * Handles caching, staleness checking, and fallbacks
   */
  async getLandingPageData(language: string): Promise<LandingPageApiResponse> {
    try {
      // First, try to get cached data
      const cachedData = await this.getCachedData(language);
      
      if (cachedData) {
        // Check if cached data is stale
        const isStale = await this.checkIfDataIsStale(language, cachedData);
        
        if (!isStale) {
          // Return fresh cached data
          return cachedData.data;
        }
      }

      // Fetch fresh data from API
      const { data, lastModified } = await this.fetchFreshData(language);
      
      // Cache the fresh data
      await this.saveCachedData(language, data, lastModified);
      
      return data;
      
    } catch (error) {
      console.error('Error fetching landing page data:', error);
      
      // Fallback to cache if API fails
      const cachedData = await this.getCachedData(language);
      if (cachedData) {
        return cachedData.data;
      }
      throw error;
    }
  }

  /**
   * Clears cached data for a specific language or all languages
   */
  async clearCache(language?: string): Promise<void> {
    try {
      if (language) {
        const cacheKey = this.getCacheKey(language);
        await AsyncStorage.removeItem(cacheKey);
      } else {
        // Clear all landing page cache
        const keys = await AsyncStorage.getAllKeys();
        const landingPageKeys = keys.filter(key => key.startsWith(this.cacheKeyPrefix));
        await AsyncStorage.multiRemove(landingPageKeys);
      }
    } catch (error) {
      console.warn('Error clearing landing page cache:', error);
    }
  }


}

export const landingPageApiService = LandingPageApiService.getInstance();
