/**
 * Onboarding API Service
 * Handles fetching onboarding content from the API
 */

import { environmentManager } from '../config/EnvironmentConfig';
import { localizationService } from '../locales/LocalizationService';

export interface OnboardingImage {
  ios: {
    '1x': string;
    '2x': string;
    '3x': string;
  };
  android: {
    hdpi: string;
    xhdpi: string;
    xxhdpi: string;
    xxxhdpi: string;
  };
}

export interface OnboardingItem {
  image: OnboardingImage;
  imageTitle: string;
  title: string;
  description: string;
  sub_description: string;
  CTA: string;
  backgroundColor: string;
  ctaTextColor: string;
}

export interface OnboardingData {
  onboarding: OnboardingItem[];
}

class OnboardingApiService {
  private readonly CACHE_KEY = 'onboarding_cache';
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Get the appropriate image URL for the current platform and density
   */
  getImageUrl(image: OnboardingImage): string {
    const platform = require('react-native').Platform.OS;
    const pixelRatio = require('react-native').PixelRatio.get();

    if (platform === 'ios') {
      if (pixelRatio >= 3) return image.ios['3x'];
      if (pixelRatio >= 2) return image.ios['2x'];
      return image.ios['1x'];
    } else {
      // Android
      if (pixelRatio >= 3.5) return image.android.xxxhdpi;
      if (pixelRatio >= 2.5) return image.android.xxhdpi;
      if (pixelRatio >= 1.5) return image.android.xhdpi;
      return image.android.hdpi;
    }
  }

  /**
   * Build the onboarding API URL based on current environment and locale
   */
  private buildOnboardingUrl(): string {
    // Use staging URL for onboarding content as specified in requirements
    // Note: Using HTTPS as the server redirects HTTP to HTTPS
    const baseUrl = 'https://stg-media.test.subway.com';
    const locale = localizationService.getCurrentLanguage();
    
    return `${baseUrl}/digital/Mobile/Onboarding/na/${locale}.json`;
  }

  /**
   * Get cache key for current locale
   */
  private getCacheKey(): string {
    const locale = localizationService.getCurrentLanguage();
    return `${this.CACHE_KEY}_${locale}`;
  }

  /**
   * Check if cached data is still valid
   */
  private async isCacheValid(): Promise<boolean> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const cacheTimestamp = await AsyncStorage.getItem(`${this.getCacheKey()}_timestamp`);
      
      if (!cacheTimestamp) return false;
      
      const timestamp = parseInt(cacheTimestamp, 10);
      const now = Date.now();
      
      return (now - timestamp) < this.CACHE_DURATION;
    } catch (error) {
      console.warn('Error checking cache validity:', error);
      return false;
    }
  }

  /**
   * Get cached onboarding data
   */
  private async getCachedData(): Promise<OnboardingData | null> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const cachedData = await AsyncStorage.getItem(this.getCacheKey());
      
      if (!cachedData) return null;
      
      return JSON.parse(cachedData);
    } catch (error) {
      console.warn('Error getting cached onboarding data:', error);
      return null;
    }
  }

  /**
   * Cache onboarding data
   */
  private async cacheData(data: OnboardingData): Promise<void> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.setItem(this.getCacheKey(), JSON.stringify(data));
      await AsyncStorage.setItem(`${this.getCacheKey()}_timestamp`, Date.now().toString());
    } catch (error) {
      console.warn('Error caching onboarding data:', error);
    }
  }

  /**
   * Fetch onboarding data from API
   */
  private async fetchFromApi(): Promise<OnboardingData> {
    const url = this.buildOnboardingUrl();

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch onboarding data: ${response.status} ${response.statusText}`);
      }

      const data: OnboardingData = await response.json();
      
      // Validate the response structure
      if (!data.onboarding || !Array.isArray(data.onboarding)) {
        throw new Error('Invalid onboarding data structure');
      }

      return data;
    } catch (error) {
      console.error('Detailed onboarding fetch error:', error);
      console.error('Error type:', typeof error);
      console.error('Error message:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  /**
   * Get onboarding data with caching support
   */
  async getOnboardingData(): Promise<OnboardingData> {
    try {
      // Check if we have valid cached data
      const isValid = await this.isCacheValid();
      
      if (isValid) {
        const cachedData = await this.getCachedData();
        if (cachedData) {
          return cachedData;
        }
      }

      const data = await this.fetchFromApi();
      await this.cacheData(data);
      return data;
    } catch (error) {
      console.error('Error fetching onboarding data:', error);
      const cachedData = await this.getCachedData();
      if (cachedData) {
        return cachedData;
      }
      throw error;
    }
  }

  /**
   * Clear onboarding cache
   */
  async clearCache(): Promise<void> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.removeItem(this.getCacheKey());
      await AsyncStorage.removeItem(`${this.getCacheKey()}_timestamp`);
    } catch (error) {
      console.warn('Error clearing onboarding cache:', error);
    }
  }
}

export const onboardingApiService = new OnboardingApiService();
