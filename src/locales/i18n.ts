/**
 * Internationalization (i18n) Configuration
 * Supports multiple locales with device detection
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as RNLocalize from 'react-native-localize';

// Import translation files
import enUS from './en-US.json';
import enCA from './en-CA.json';
import frCA from './fr-CA.json';
import enPR from './en-PR.json';
import esUS from './es-US.json';
import esPR from './es-PR.json';

// Define available languages with proper i18next structure
export const supportedLocales = {
  'en-US': {
    translation: enUS,
  },
  'en-CA': {
    translation: enCA,
  },
  'fr-CA': {
    translation: frCA,
  },
  'en-PR': {
    translation: enPR,
  },
  'es-US': {
    translation: esUS,
  },
  'es-PR': {
    translation: esPR,
  },
};

export const defaultLocale = 'en-US';

// Get the best available language based on device settings
const getLanguage = (): string => {
  const locales = RNLocalize.getLocales();
  
  if (Array.isArray(locales)) {
    for (const locale of locales) {
      const tag = locale.languageTag;
      
      // Check for exact match (e.g., "en-CA")
      if (supportedLocales[tag as keyof typeof supportedLocales]) {
        return tag;
      }
      
      // Check for language match (e.g., "en" matches "en-US")
      const language = locale.languageCode;
      const matchingLocale = Object.keys(supportedLocales).find(key => 
        key.startsWith(language)
      );
      
      if (matchingLocale) {
        return matchingLocale;
      }
    }
  }
  
  return defaultLocale;
};

// Initialize i18n
const initI18n = () => {
  const languageDetector = {
    type: 'languageDetector' as const,
    async: true,
    detect: (callback: (lang: string) => void) => {
      try {
        const language = getLanguage();
        callback(language);
      } catch (error) {
        console.log('Error detecting language:', error);
        callback(defaultLocale);
      }
    },
    init: () => {},
    cacheUserLanguage: () => {},
  };

  i18n
    .use(languageDetector)
    .use(initReactI18next)
    .init({
      resources: supportedLocales,
      fallbackLng: defaultLocale,
      debug: __DEV__,
      
      // Interpolation options
      interpolation: {
        escapeValue: false, // React already escapes values
      },
      
      // Namespace configuration
      defaultNS: 'translation',
      ns: ['translation'],
      
      // React specific options
      react: {
        useSuspense: false,
      },
    });

  return i18n;
};

// Initialize and export the configured i18n instance
initI18n();

export { i18n };
export default i18n;
