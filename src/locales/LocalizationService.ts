/**
 * Localization Service - Handles language changes and provides translation utilities
 */

import { useTranslation } from 'react-i18next';
import * as RNLocalize from 'react-native-localize';
import { i18n } from './i18n';
import { supportedLocales, defaultLocale } from './i18n';

export class LocalizationService {
  private static instance: LocalizationService;
  private observers: Array<(language: string) => void> = [];

  private constructor() {
    // Note: react-native-localize doesn't have addEventListener in newer versions
    // We'll handle locale changes through app lifecycle events
  }

  static getInstance(): LocalizationService {
    if (!LocalizationService.instance) {
      LocalizationService.instance = new LocalizationService();
    }
    return LocalizationService.instance;
  }

  // Observer pattern for language changes
  subscribe(observer: (language: string) => void): () => void {
    this.observers.push(observer);
    return () => {
      this.observers = this.observers.filter(obs => obs !== observer);
    };
  }

  private notifyObservers(language: string): void {
    this.observers.forEach(observer => observer(language));
  }

  // Detect device language
  detectLanguage(): string {
    const locales = RNLocalize.getLocales();
    
    if (Array.isArray(locales)) {
      for (const locale of locales) {
        const tag = locale.languageTag;
        
        // Check for exact match
        if (supportedLocales[tag as keyof typeof supportedLocales]) {
          return tag;
        }
        
        // Check for language match
        const language = locale.languageCode;
        const matchingLocale = Object.keys(supportedLocales).find(key => 
          key.startsWith(language)
        );
        
        if (matchingLocale) {
          return matchingLocale;
        }
      }
    }
    
    return defaultLocale;
  }

  // Change language programmatically
  async changeLanguage(language: string): Promise<void> {
    if (supportedLocales[language as keyof typeof supportedLocales]) {
      await i18n.changeLanguage(language);
      this.notifyObservers(language);
    } else {
      console.warn(`Language ${language} not supported, falling back to ${defaultLocale}`);
      await i18n.changeLanguage(defaultLocale);
      this.notifyObservers(defaultLocale);
    }
  }

  // Get current language
  getCurrentLanguage(): string {
    return i18n.language || defaultLocale;
  }

  // Get available languages
  getAvailableLanguages(): string[] {
    return Object.keys(supportedLocales);
  }

  // Get language display name
  getLanguageDisplayName(language: string): string {
    const names: { [key: string]: string } = {
      'en-US': 'English (US)',
      'en-CA': 'English (Canada)',
      'fr-CA': 'Français (Canada)',
      'en-PR': 'English (Puerto Rico)',
      'es-US': 'Español (US)',
      'es-PR': 'Español (Puerto Rico)',
    };
    return names[language] || language;
  }

  // Get device locale info
  getDeviceLocaleInfo() {
    return {
      locales: RNLocalize.getLocales(),
      country: RNLocalize.getCountry(),
      currencies: RNLocalize.getCurrencies(),
      timeZone: RNLocalize.getTimeZone(),
      uses24HourClock: RNLocalize.uses24HourClock(),
      usesMetricSystem: RNLocalize.usesMetricSystem(),
    };
  }

  // Format currency based on locale
  formatCurrency(amount: number, currency: string = 'USD'): string {
    const locale = this.getCurrentLanguage();
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
      }).format(amount);
    } catch (error) {
      // Fallback formatting
      return `$${amount.toFixed(2)}`;
    }
  }

  // Format date based on locale
  formatDate(date: Date): string {
    const locale = this.getCurrentLanguage();
    try {
      return new Intl.DateTimeFormat(locale).format(date);
    } catch (error) {
      // Fallback formatting
      return date.toLocaleDateString();
    }
  }

  // Format time based on locale
  formatTime(date: Date): string {
    const locale = this.getCurrentLanguage();
    const uses24Hour = RNLocalize.uses24HourClock();
    
    try {
      return new Intl.DateTimeFormat(locale, {
        hour: 'numeric',
        minute: '2-digit',
        hour12: !uses24Hour,
      }).format(date);
    } catch (error) {
      // Fallback formatting
      return date.toLocaleTimeString();
    }
  }
}

// Singleton instance
export const localizationService = LocalizationService.getInstance();

// Hook for using translations in components
export const useLocalization = () => {
  const { t, i18n } = useTranslation();
  
  return {
    t,
    currentLanguage: i18n.language,
    changeLanguage: localizationService.changeLanguage.bind(localizationService),
    availableLanguages: localizationService.getAvailableLanguages(),
    getLanguageDisplayName: localizationService.getLanguageDisplayName.bind(localizationService),
    formatCurrency: localizationService.formatCurrency.bind(localizationService),
    formatDate: localizationService.formatDate.bind(localizationService),
    formatTime: localizationService.formatTime.bind(localizationService),
  };
};
