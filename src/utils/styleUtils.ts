// Generic style utilities for use across the app
import { Platform, PixelRatio } from 'react-native';
import { StyleConfig } from '../models/LandingPageModels'; // Consider moving StyleConfig to a more generic location if needed

export const normalizeFontWeight = (weight?: string): any => {
  if (!weight) return 'normal';
  switch (weight.toLowerCase()) {
    case 'bold': return 'bold';
    case 'normal': return 'normal';
    case '100': case 'thin': return '100';
    case '200': case 'ultralight': return '200';
    case '300': case 'light': return '300';
    case '400': case 'regular': return '400';
    case '500': case 'medium': return '500';
    case '600': case 'semibold': return '600';
    case '700': return '700';
    case '800': case 'heavy': return '800';
    case '900': case 'black': return '900';
    default: return 'normal';
  }
};

export const getPlatformStyle = (styleConfig?: { android?: StyleConfig; ios?: StyleConfig }): StyleConfig => {
  if (!styleConfig) return {};
  const platformStyle = Platform.OS === 'ios' ? styleConfig.ios : styleConfig.android;
  return platformStyle || styleConfig.android || styleConfig.ios || {};
};

export const getPlatformImageUrl = (imageConfig?: any): string => {
  if (!imageConfig) return '';
  const platform = Platform.OS === 'ios' ? 'ios' : 'android';
  const platformImages = imageConfig[platform];
  if (!platformImages) {
    const fallbackPlatform = platform === 'ios' ? 'android' : 'ios';
    return imageConfig[fallbackPlatform]?.url || '';
  }
  if (Platform.OS === 'ios') {
    const scale = PixelRatio.get();
    if (scale >= 3 && platformImages['3x']) {
      return platformImages['3x'];
    } else if (scale >= 2 && platformImages['2x']) {
      return platformImages['2x'];
    } else if (platformImages['1x']) {
      return platformImages['1x'];
    }
  } else {
    const density = PixelRatio.get();
    if (density >= 4 && platformImages.xxxhdpi) {
      return platformImages.xxxhdpi;
    } else if (density >= 3 && platformImages.xxhdpi) {
      return platformImages.xxhdpi;
    } else if (density >= 2 && platformImages.xhdpi) {
      return platformImages.xhdpi;
    } else if (platformImages.hdpi) {
      return platformImages.hdpi;
    }
  }
  return platformImages.url || 
         platformImages['2x'] || 
         platformImages.xhdpi || 
         Object.values(platformImages)[0] || 
         '';
};
