/**
 * Build Information Utility
 * Extracts version and build information from the app
 */

import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

// Import package.json for version
const packageJson = require('../../package.json');

export interface BuildInfo {
  appName: string;
  version: string;
  buildNumber: string;
  platform: string;
  environment: string;
}

export class BuildInfoService {
  private static instance: BuildInfoService;

  private constructor() {}

  static getInstance(): BuildInfoService {
    if (!BuildInfoService.instance) {
      BuildInfoService.instance = new BuildInfoService();
    }
    return BuildInfoService.instance;
  }

  /**
   * Get app name from package.json
   */
  getAppName(): string {
    // In a real app, this might come from a display name config
    return packageJson.displayName || packageJson.name || 'SubwayApp';
  }

  /**
   * Get version from package.json
   */
  getVersion(): string {
    return packageJson.version || '1.0.0';
  }

  /**
   * Get build number (for now, use timestamp or version, in real app this would come from CI/CD)
   */
  getBuildNumber(): string {
    // Uses react-native-device-info for both Android and iOS
    try {
      return DeviceInfo.getBuildNumber();
    } catch {
      return 'unknown';
    }
  }

  /**
   * Get platform information
   */
  getPlatform(): string {
    return Platform.OS;
  }

  /**
   * Get all build information
   */
  getBuildInfo(): BuildInfo {
    return {
      appName: this.getAppName(),
      version: this.getVersion(),
      buildNumber: this.getBuildNumber(),
      platform: this.getPlatform(),
      environment: 'development', // This could be injected during build
    };
  }

  /**
   * Get formatted build string for display
   */
  getFormattedBuildString(): string {
    const info = this.getBuildInfo();
    return `${info.version} (${info.buildNumber})`;
  }
}

export const buildInfoService = BuildInfoService.getInstance();
