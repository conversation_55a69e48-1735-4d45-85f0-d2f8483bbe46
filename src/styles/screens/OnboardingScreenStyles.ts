/**
 * Onboarding Screen Styles
 */

import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '../styles';

const { width: screenWidth } = Dimensions.get('window');

export const onboardingStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  loadingText: {
    color: colors.textSecondary,
    fontSize: 16,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: 20,
  },
  errorText: {
    color: colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  retryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  skipButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  skipButtonText: {
    color: colors.primary,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width: screenWidth,
    flex: 1,
  },
  // Top Image Section (50% of screen)
  topImageSection: {
    height: '50%',
    position: 'relative',
  },
  topImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 60, // Small margin from top/status bar
    left: 0,
    right: 0,
    paddingHorizontal: 30,
  },
  imageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    textAlign: 'center',
    letterSpacing: 1.2,
    textTransform: 'uppercase',
    lineHeight: 32,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  // Bottom Content Section (50% of screen)
  bottomContentSection: {
    height: '50%',
    backgroundColor: colors.white,
    paddingHorizontal: 40,
    paddingTop: 40,
    paddingBottom: 100, // Space for navigation
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 34,
  },
  description: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 10,
  },
  subDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Navigation Section (Fixed at bottom)
  navigationSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    paddingVertical: 20,
    paddingBottom: 40,
    alignItems: 'center',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  indicator: {
    borderRadius: 6,
    marginHorizontal: 6,
  },
  activeIndicator: {
    width: 13,
    height: 13,
    backgroundColor: colors.textSecondary,
    opacity: 1,
  },
  inactiveIndicator: {
    width: 11,
    height: 11,
    backgroundColor: colors.lightGray,
    opacity: 1,
  },
  ctaContainer: {
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  ctaText: {
    fontSize: 18,
    color: colors.text,
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
});
