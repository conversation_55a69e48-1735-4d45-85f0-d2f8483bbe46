/**
 * Landing Page Styles
 */

import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '../styles';

const { height } = Dimensions.get('window');

export const landingPageStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    flexGrow: 1,
  },
  paddedContent: {
    paddingHorizontal: 20,
    paddingBottom: 10,
    paddingTop: 0,
    marginTop: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
  },
  loadingText: {
    color: colors.white,
    fontSize: 16,
    marginTop: 15,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    padding: 20,
  },
  errorText: {
    color: colors.white,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  retryButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  imageContainer: {
  },
  mainImage: {
    width: '100%',
    height: 250,
    resizeMode: 'cover',
  },
  mvpIconContainer: {
    alignItems: 'center',
    marginBottom: 0,
    paddingVertical: 0,
  },
  mvpIcon: {
    width: 120,
    height: 50,
    marginTop: 40,
    marginBottom: 0,
    paddingBottom: 0
  },
  headline: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
    color: colors.text
  },
  subHeadline: {
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 30,
    marginBottom: 30,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  buttonsContainer: {
    paddingHorizontal: 30,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: colors.subwayGreen,
    paddingVertical: 9,
    paddingHorizontal: 28,
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 2,
  },
  primaryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    paddingVertical: 9,
    paddingHorizontal: 28,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.subwayGreen,
    alignItems: 'center',
    marginBottom: 4,
  },
  secondaryButtonText: {
    color: colors.subwayGreen,
    fontSize: 16,
    fontWeight: 'normal',
  },
  linkButton: {
    alignItems: 'center',
    paddingVertical: 6,
  },
  linkButtonText: {
    fontSize: 14,
    color: colors.subwayGreen,
    textDecorationLine: 'underline',
  },
  header: {
    height: height * 0.4,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  // Animation and wrapper styles
  animatedContainer: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  // Status bar styles
  statusBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  iosStatusBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  contentContainerOverride: {
    paddingVertical: 0,
    justifyContent: 'flex-start',
  },
  // iOS-specific content spacing
  iosContentSpacer: {
    backgroundColor: 'transparent',
  },
  // MVP icon and headline container
  mvpHeadlineContainer: {
    alignItems: 'center',
    marginTop: 0,
    marginBottom: 0,
    paddingVertical: 0,
  },
  // Loading overlay styles
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Lottie animation styles
  lottieAnimation: {
    width: 100,
    height: 35,
  },
  // Button loading states
  buttonLoadingState: {
    opacity: 0.8,
  },
  // Continue as Guest container
  guestButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 20,
    width: '100%',
  },
  guestButtonOverride: {
    paddingVertical: 0,
  },
  // Skeleton loading styles
  skeletonMainImage: {
    backgroundColor: '#f0f0f0',
  },
  skeletonMvpIcon: {
    opacity: 0.2,
  },
  skeletonHeadline: {
    backgroundColor: '#f5f5f5',
    height: 24,
    borderRadius: 12,
    marginHorizontal: 60,
    marginTop: 5,
    marginBottom: 8,
    opacity: 0.6,
  },
  skeletonSubHeadline: {
    backgroundColor: '#f5f5f5',
    height: 16,
    borderRadius: 8,
    marginHorizontal: 40,
    marginBottom: 25,
    opacity: 0.6,
  },
  skeletonPrimaryButton: {
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
  },
  skeletonSecondaryButton: {
    backgroundColor: 'transparent',
    borderColor: '#e0e0e0',
    borderRadius: 4,
  },
  skeletonLinkButton: {
    backgroundColor: 'transparent',
    height: 24,
    borderRadius: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  skeletonLinkText: {
    backgroundColor: '#e8e8e8',
    height: 16,
    width: 120,
    borderRadius: 3,
  },
  // Button text skeleton styles
  skeletonButtonText: {
    backgroundColor: '#d8d8d8',
    height: 18,
    borderRadius: 3,
    width: '60%',
  },
  skeletonButtonTextSecondary: {
    backgroundColor: '#e8e8e8',
    height: 16,
    borderRadius: 3,
    width: '50%',
  },
  // Shimmer effect for skeleton elements
  skeletonShimmer: {
    opacity: 0.8,
  },
});