/**
 * Global Styles and Theme Configuration
 */

// Color palette
export const colors = {
  // Primary brand colors
  brand: '#008938', // Changed from blue to white for splash screen
  primary: '#FFFFFF',
  secondary: '#004b18',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  text: '#333333',
  textSecondary: '#666666',
  textLight: '#999999',
  textInverse: '#FFFFFF',
  
  // Gray variations
  darkGray: '#444444',
  lightGray: '#F0F0F0',
  
  // Background colors
  background: '#FFFFFF',
  backgroundSecondary: '#F5F5F5',
  backgroundLight: '#FAFAFA',
  backgroundMedium: '#E5E5E5',
  backgroundSkeleton: '#E0E0E0',
  
  // Border and divider colors
  border: '#E0E0E0',
  borderGray: '#CCCCCC',
  divider: '#E0E0E0',
  
  // Interactive colors
  link: '#0066cc',
  linkHover: '#004499',
  
  // Status colors
  success: '#00a651',
  warning: '#FFA500',
  error: '#FF0000',
  info: '#0066cc',
  
  // Subway brand specific
  subwayGreen: '#008938',
  subwayYellow: '#FFC72C',
};

// Typography
export const fonts = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  weights: {
    normal: '400' as const,
    medium: '500' as const,
    bold: '700' as const,
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Border radius
export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

// Shadows
export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Common button styles
export const buttonStyles = {
  primary: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  secondary: {
    backgroundColor: 'transparent',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.primary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
};

// Text styles
export const textStyles = {
  h1: {
    fontSize: fonts.sizes.xxxl,
    fontWeight: fonts.weights.bold,
    color: colors.text,
  },
  h2: {
    fontSize: fonts.sizes.xxl,
    fontWeight: fonts.weights.bold,
    color: colors.text,
  },
  h3: {
    fontSize: fonts.sizes.xl,
    fontWeight: fonts.weights.medium,
    color: colors.text,
  },
  body: {
    fontSize: fonts.sizes.md,
    fontWeight: fonts.weights.normal,
    color: colors.text,
  },
  caption: {
    fontSize: fonts.sizes.sm,
    fontWeight: fonts.weights.normal,
    color: colors.textSecondary,
  },
};

// Layout constants
export const layout = {
  screenPadding: spacing.md,
  contentMaxWidth: 400,
  headerHeight: 60,
};
