/**
 * App Controller - Handles user interactions between Model and View
 */

import { AppModel, appModel } from '../models/AppModel';

export type Screen = 'SplashScreen' | 'LandingPage' | 'OnboardingScreen';

export interface SimpleNavigation {
  navigate: (screen: Screen) => void;
  currentScreen: Screen;
}

export class AppController {
  private model: AppModel;
  private navigation?: SimpleNavigation;

  constructor(model: AppModel) {
    this.model = model;
  }

  setNavigation(navigation: SimpleNavigation): void {
    this.navigation = navigation;
  }

  // Initialization
  async initializeApp(): Promise<void> {
    try {
      await this.model.initializeApp();
      this.navigateToLandingPage();
    } catch (error) {
      console.error('App initialization failed:', error);
    }
  }


  // Navigation methods (only for app-level navigation)
  navigateToLandingPage(): void {
    this.setCurrentScreen('LandingPage');
  }

  navigateToOnboarding(): void {
    this.setCurrentScreen('OnboardingScreen');
  }

  setCurrentScreen(screen: string): void {
    this.model.setCurrentScreen(screen);
  }

  // Getters for views
  getAppState() {
    return this.model.getState();
  }

  isLoading(): boolean {
    return this.model.isLoading();
  }

  getCurrentUser() {
    return this.model.getUser();
  }

  // Subscribe to model changes
  subscribeToStateChanges(callback: (state: any) => void): () => void {
    return this.model.subscribe(callback);
  }
}

// Singleton instance
export const appController = new AppController(appModel);
