import { AppModel } from '../models/AppModel';

export class LandingPageController {
  private model: AppModel;

  constructor(model: AppModel) {
    this.model = model;
  }

  async handleLogin(email: string, password: string): Promise<boolean> {
    try {
      return await this.model.loginUser(email, password);
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  }

  handleLogout(): void {
    this.model.logoutUser();
  }

  getCurrentUser() {
    return this.model.getUser();
  }

  isLoading(): boolean {
    return this.model.isLoading();
  }

  getAppState() {
    return this.model.getState();
  }

  subscribeToStateChanges(callback: (state: any) => void): () => void {
    return this.model.subscribe(callback);
  }
}
