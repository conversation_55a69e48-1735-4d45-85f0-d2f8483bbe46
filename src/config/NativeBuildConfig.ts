/**
 * Native Build Config Module
 * Provides access to Android BuildConfig values in React Native
 */

import { NativeModules } from 'react-native';
import DeviceInfo from 'react-native-device-info';

// Read bundle ID synchronously
const bundleId = DeviceInfo.getBundleId();

// Fetch native module values if available
const native = NativeModules.BuildConfigModule as {
  ENVIRONMENT?: string;
  IS_PRODUCTION_BUILD?: boolean;
} | undefined;

// Determine environment and production flag with fallbacks
let ENVIRONMENT = native?.ENVIRONMENT ?? 'production';
let IS_PRODUCTION_BUILD = native?.IS_PRODUCTION_BUILD ?? true;

// Enforce non-production for .lower bundle IDs
if (bundleId.includes('.lower')) {
  console.warn(`NativeBuildConfig: Detected .lower bundleId '${bundleId}'. Forcing non-production.`);
  ENVIRONMENT = 'cfa-uat';
  IS_PRODUCTION_BUILD = false;
}

// Log resolved configuration for debugging
console.log('NativeBuildConfig: Final config =>', { bundleId, ENVIRONMENT, IS_PRODUCTION_BUILD });

// Export final config object
export default {
  BUNDLE_ID: bundleId,
  ENVIRONMENT,
  IS_PRODUCTION_BUILD,
};
