/**
 * Environment Configuration
 * Manages different environments and their configurations
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { productionConfig, cfaUatConfig, cfaQeConfig } from './environments';
import BuildConfigModule from './NativeBuildConfig';

// Determine build configuration from native modules (both iOS and Android)
/**
 * Retrieve build configuration values, with enforced overrides for Lower bundle ID.
 */
function getBuildConfig(): { ENVIRONMENT: string; IS_PRODUCTION_BUILD: boolean; BUNDLE_ID: string } {
  try {
    const bundleId = BuildConfigModule.BUNDLE_ID ?? '';
    let env = BuildConfigModule.ENVIRONMENT;
    let isProductionBuild = BuildConfigModule.IS_PRODUCTION_BUILD;
    // Enforce non-production for .lower bundle IDs
    if (bundleId.endsWith('.lower')) {
      if (isProductionBuild) {
        console.warn(
          `EnvironmentConfig: OVERRIDE! Bundle ID '${bundleId}' reported production build. Forcing IS_PRODUCTION_BUILD=false.`
        );
      }
      env = 'cfa-uat';
      isProductionBuild = false;
    }
    console.log('EnvironmentConfig: getBuildConfig =>', { bundleId, env, isProductionBuild });
    return { ENVIRONMENT: env, IS_PRODUCTION_BUILD: isProductionBuild, BUNDLE_ID: bundleId };
  } catch (error) {
    console.warn('EnvironmentConfig: Failed to get build config from native module, using fallback:', error);
    const bundleId = '';
    const env = __DEV__ ? 'cfa-uat' : 'production';
    const isProductionBuild = false;
    return { ENVIRONMENT: env, IS_PRODUCTION_BUILD: isProductionBuild, BUNDLE_ID: bundleId };
  }
}

// Additional function to detect lower build via bundle identifier
async function isLowerBuildFromBundleId(): Promise<boolean> {
  try {
    const bundleId = await DeviceInfo.getBundleId();
    return bundleId.includes('.lower');
  } catch (error) {
    return false;
  }
}

/**
 * Dynamic proxy for build config values to ensure fresh values on each access
 */
const BUILD_CONFIG = {
  get IS_PRODUCTION_BUILD(): boolean {
    return getBuildConfig().IS_PRODUCTION_BUILD;
  },
  get ENVIRONMENT(): string {
    return getBuildConfig().ENVIRONMENT;
  },
  get BUNDLE_ID(): string {
    return getBuildConfig().BUNDLE_ID;
  },
};

export interface EnvironmentConfig {
  name: string;
  displayName: string;
  apiBaseUrl: string;
  mediaBaseUrl: string;
  isProduction: boolean;
}

export const ENVIRONMENTS: { [key: string]: EnvironmentConfig } = {
  production: productionConfig,
  'cfa-uat': cfaUatConfig,
  'cfa-qe': cfaQeConfig,
};

export class EnvironmentManager {
  private static instance: EnvironmentManager;
  private currentEnvironment: EnvironmentConfig;
  private readonly STORAGE_KEY = 'selected_environment';
  private isLowerBuild: boolean | null = null;

  private constructor() {
    // Set a temporary default environment. The real one is set in initialize().
    this.currentEnvironment = productionConfig;
    this.checkIfLowerBuild();
  }

  private async checkIfLowerBuild() {
    this.isLowerBuild = await isLowerBuildFromBundleId();
  }

  private async getInitialEnvironment(): Promise<EnvironmentConfig> {
    // Determine bundle ID first to handle "lower" builds
    const bundleId = await DeviceInfo.getBundleId();
    // If bundle ID is for "lower", always force non-production environment
    if (bundleId.includes('.lower')) {
      return ENVIRONMENTS['cfa-uat'];
    }

    // If this is a production build, it must be the production environment.
    if (BUILD_CONFIG.IS_PRODUCTION_BUILD) {
      return ENVIRONMENTS.production;
    }

    // Original logic for non-production builds
    if (BUILD_CONFIG.ENVIRONMENT === 'production') {
      return ENVIRONMENTS.production;
    } else if (BUILD_CONFIG.ENVIRONMENT === 'cfa-qe') {
      return ENVIRONMENTS['cfa-qe'];
    }
    
    // Fallback for standard development builds
    return __DEV__ ? ENVIRONMENTS['cfa-uat'] : ENVIRONMENTS.production;
  }

  static getInstance(): EnvironmentManager {
    if (!EnvironmentManager.instance) {
      EnvironmentManager.instance = new EnvironmentManager();
    }
    return EnvironmentManager.instance;
  }

  /**
   * Initialize environment from stored preference
   */
  async initialize(): Promise<void> {
    try {
      // Retrieve build config values
      const buildConfig = getBuildConfig();
      const bundleId = buildConfig.BUNDLE_ID;
      const isLower = bundleId.includes('.lower');
      // Get the initial environment based on build flags first
      let initialEnv = await this.getInitialEnvironment();

      // Force reset for .lower builds (should never be production)
      if (isLower && initialEnv.isProduction) {
        console.warn("EnvironmentConfig: OVERRIDE! .lower build detected as production. Forcing to 'cfa-uat'.");
        initialEnv = ENVIRONMENTS['cfa-uat'];
      }
      this.currentEnvironment = initialEnv;

      // Load stored environment only if switching is allowed
      if (await this.isEnvironmentSwitchingAllowed()) {
        const storedEnv = await AsyncStorage.getItem(this.STORAGE_KEY);
        if (storedEnv && ENVIRONMENTS[storedEnv]) {
          const availableEnvs = await this.getAvailableEnvironments();
          if (availableEnvs.some(env => env.name === storedEnv)) {
            // Ensure .lower build cannot switch to production
            if (!isLower || (isLower && !ENVIRONMENTS[storedEnv].isProduction)) {
              this.currentEnvironment = ENVIRONMENTS[storedEnv];
            }
          }
        }
      }

      // Final check: A .lower build must never run on production.
      if (isLower && this.currentEnvironment.isProduction) {
        this.currentEnvironment = ENVIRONMENTS['cfa-uat'];
        await AsyncStorage.setItem(this.STORAGE_KEY, 'cfa-uat');
        throw new Error("FATAL: .lower build was configured for production. Resetting to 'cfa-uat'. Please restart the app.");
      }

    } catch (error) {
      console.error('Error during environment initialization:', error);
      // If we get here from our fatal error, show an alert
      if (error instanceof Error && error.message.includes('FATAL')) {
        Alert.alert("Configuration Error", error.message);
      }
    }
  }

  /**
   * Get current environment configuration
   */
  getCurrentEnvironment(): EnvironmentConfig {
    return this.currentEnvironment;
  }

  /**
   * Get all available environments
   */
  async getAllEnvironments(): Promise<EnvironmentConfig[]> {
    return await this.getAvailableEnvironments();
  }

  /**
   * Switch to a different environment
   */
  async switchEnvironment(environmentName: string): Promise<boolean> {
    // Check if environment switching is allowed
    if (!await this.isEnvironmentSwitchingAllowed()) {
      console.warn('Environment switching not allowed in production builds');
      return false;
    }

    if (!ENVIRONMENTS[environmentName]) {
      console.error('Invalid environment:', environmentName);
      return false;
    }

    // Check if the environment is available for current build type
    const availableEnvs = await this.getAvailableEnvironments();
    const isAllowed = availableEnvs.some(env => env.name === environmentName);
    
    if (!isAllowed) {
      console.error('Environment not available for current build type:', environmentName);
      return false;
    }

    try {
      this.currentEnvironment = ENVIRONMENTS[environmentName];
      await AsyncStorage.setItem(this.STORAGE_KEY, environmentName);
      
      // Show confirmation and restart prompt
      Alert.alert(
        'Environment Changed',
        `Switched to ${this.currentEnvironment.displayName}. The app will restart to apply changes.`,
        [
          {
            text: 'Restart Now',
            onPress: () => this.restartApp(),
          },
        ]
      );

      return true;
    } catch (error) {
      console.error('Error switching environment:', error);
      return false;
    }
  }

  /**
   * Check if current environment is production
   */
  isProduction(): boolean {
    return this.currentEnvironment.isProduction;
  }

  /**
   * Check if debug drawer should be available
   * Debug drawer is available in debug builds (regardless of environment)
   * Also enabled for lower builds to support debugging in non-dev mode
   */
  isDebugDrawerEnabled(): boolean {    
    return !BUILD_CONFIG.IS_PRODUCTION_BUILD;;
  }

  /**
   * Get API base URL for current environment
   */
  getApiBaseUrl(): string {
    return this.currentEnvironment.apiBaseUrl;
  }

  /**
   * Get media base URL for current environment
   */
  getMediaBaseUrl(): string {
    return this.currentEnvironment.mediaBaseUrl;
  }

  /**
   * Get environment display name
   */
  getEnvironmentDisplayName(): string {
    return this.currentEnvironment.displayName;
  }

  /**
   * Restart the app (requires react-native-restart or similar)
   */
  private restartApp(): void {
    // For now, we'll show an alert asking user to manually restart
    // In a real app, you might use react-native-restart
    Alert.alert(
      'Restart Required',
      'Please close and reopen the app to apply environment changes.',
      [{ text: 'OK' }]
    );
  }

  /**
   * Clear stored environment (reset to prod)
   */
  async resetToProduction(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      this.currentEnvironment = ENVIRONMENTS.prod;
    } catch (error) {
      console.warn('Error resetting environment:', error);
    }
  }

  /**
   * Check if environment switching is allowed (only in lower builds)
   */
  async isEnvironmentSwitchingAllowed(): Promise<boolean> {
    return !BUILD_CONFIG.IS_PRODUCTION_BUILD;
  }

  /**
   * Get available environments for current build type
   */
  async getAvailableEnvironments(): Promise<EnvironmentConfig[]> {
    const bundleId = await DeviceInfo.getBundleId();

    if (bundleId.includes('.lower')) {
      // Lower builds have access to UAT and QE environments only
      return [ENVIRONMENTS['cfa-uat'], ENVIRONMENTS['cfa-qe']];
    }

    if (BUILD_CONFIG.IS_PRODUCTION_BUILD) {
      // Production builds only have access to production environment
      return [ENVIRONMENTS.production];
    } else {
      // All non-production environments are available for other builds
      const envs = Object.values(ENVIRONMENTS).filter(env => !env.isProduction);
      return envs.length > 0 ? envs : [ENVIRONMENTS['cfa-uat'], ENVIRONMENTS['cfa-qe']];
    }
  }
}

export const environmentManager = EnvironmentManager.getInstance();