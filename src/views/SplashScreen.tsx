/**
 * Splash Screen View - Animated Lottie splash screen
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StatusBar,
} from 'react-native';
import LottieView from 'lottie-react-native';
import { appController, SimpleNavigation } from '../controllers/AppController';
import { appModel } from '../models/AppModel';
import { localizationService } from '../locales/LocalizationService';
import { splashScreenStyles } from '../styles/screens/SplashScreenStyles';
import { colors } from '../styles/styles';

interface SplashScreenProps {
  navigation: SimpleNavigation;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  const animationRef = useRef<LottieView>(null);
  const [isAppReady, setIsAppReady] = useState(false);

  useEffect(() => {
    // Set navigation in controller
    appController.setNavigation(navigation);
    
    // Detect and set language
    localizationService.detectLanguage();
    
    // Start the animation
    animationRef.current?.play();
    
    // Initialize app and wait for completion
    const initializeApp = async () => {
      try {
        await appModel.initializeApp(); // Call model directly to avoid auto-navigation
        
        // Also pre-fetch landing page data to avoid loading screen
        const { landingPageApiService } = await import('../services/LandingPageApiService');
        const { localizationService } = await import('../locales/LocalizationService');
        
        try {
          const currentLanguage = localizationService.getCurrentLanguage();
          await landingPageApiService.getLandingPageData(currentLanguage);
        } catch (apiError) {
          console.log('Landing page API pre-fetch failed, will show retry on landing page:', apiError);
        }
        
        // Small delay to ensure smooth transition
        setTimeout(() => {
          setIsAppReady(true);
        }, 500);
      } catch (error) {
        console.error('App initialization error:', error);
        // Still navigate even if there's an error
        setTimeout(() => {
          setIsAppReady(true);
        }, 2000);
      }
    };
    
    initializeApp();
  }, [navigation]);

  // Navigate when app is ready
  useEffect(() => {
    if (isAppReady) {
      // Navigate to landing page
      appController.navigateToLandingPage();
    }
  }, [isAppReady]);

  return (
    <View style={splashScreenStyles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={colors.white}
        translucent={true}
        hidden={false}
      />
      
      <LottieView
        ref={animationRef}
        source={require('../assets/splash_lottie.json')}
        style={splashScreenStyles.lottieAnimation}
        autoPlay={true}
        loop={true}
        speed={1}
        resizeMode="cover"
      />
    </View>
  );
};

export default SplashScreen;
