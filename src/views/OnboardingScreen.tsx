/**
 * Onboarding Screen
 * ViewPager-like onboarding experience with swipeable slides
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  StatusBar,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { onboardingApiService, OnboardingData, OnboardingItem } from '../services/OnboardingApiService';
import { onboardingStyles } from '../styles/screens/OnboardingScreenStyles';
import { colors } from '../styles/styles';

interface OnboardingScreenProps {
  navigation: {
    navigate: (screen: 'SplashScreen' | 'LandingPage' | 'OnboardingScreen') => void;
    currentScreen: 'SplashScreen' | 'LandingPage' | 'OnboardingScreen';
  };
}

const { width: screenWidth } = Dimensions.get('window');

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ navigation }) => {
  const [onboardingData, setOnboardingData] = useState<OnboardingItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    fetchOnboardingData();
  }, []);

  const fetchOnboardingData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data: OnboardingData = await onboardingApiService.getOnboardingData();
      setOnboardingData(data.onboarding);
    } catch (err) {
      console.error('Error fetching onboarding data:', err);
      setError('Failed to load onboarding content');
    } finally {
      setIsLoading(false);
    }
  };

  const handleScroll = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / screenWidth);
    setCurrentIndex(index);
  };

  const goToSlide = (index: number) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: index * screenWidth,
        animated: true,
      });
    }
    setCurrentIndex(index);
  };

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      goToSlide(currentIndex + 1);
    }
  };

  const handleDismiss = () => {
    navigation.navigate('LandingPage');
  };

  const isLastSlide = currentIndex === onboardingData.length - 1;

  if (isLoading) {
    return (
      <View style={onboardingStyles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.brand} />
        <Text style={onboardingStyles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (error || onboardingData.length === 0) {
    return (
      <View style={onboardingStyles.errorContainer}>
        <Text style={onboardingStyles.errorText}>{error || 'No onboarding content available'}</Text>
        <TouchableOpacity style={onboardingStyles.retryButton} onPress={fetchOnboardingData}>
          <Text style={onboardingStyles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
        <TouchableOpacity style={onboardingStyles.skipButton} onPress={handleDismiss}>
          <Text style={onboardingStyles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={onboardingStyles.container}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor="transparent"
        translucent={true}
      />
      
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        scrollEventThrottle={16}
        style={onboardingStyles.scrollView}
      >
        {onboardingData.map((item, index) => {
          const imageUrl = onboardingApiService.getImageUrl(item.image);
          
          return (
            <View key={index} style={onboardingStyles.slide}>
              {/* Top Image Section - 60% of screen */}
              <View style={onboardingStyles.topImageSection}>
                <FastImage
                  source={{ 
                    uri: imageUrl,
                    priority: FastImage.priority.high,
                  }}
                  style={onboardingStyles.topImage}
                  resizeMode={FastImage.resizeMode.cover}
                  onLoad={() => console.log(`Image loaded for slide ${index}`)}
                  onError={() => console.error(`Image failed for slide ${index}`)}
                />
                
                {/* Image title overlay */}
                {item.imageTitle && (
                  <View style={onboardingStyles.imageOverlay}>
                    <Text style={onboardingStyles.imageTitle}>{item.imageTitle}</Text>
                  </View>
                )}
              </View>
              
              {/* Bottom Content Section - 40% of screen */}
              <View style={onboardingStyles.bottomContentSection}>
                <Text style={onboardingStyles.title}>{item.title}</Text>
                <Text style={onboardingStyles.description}>{item.description}</Text>
                {item.sub_description && (
                  <Text style={onboardingStyles.subDescription}>{item.sub_description}</Text>
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>

      {/* Navigation Section - Fixed at bottom */}
      <View style={onboardingStyles.navigationSection}>        
        {/* Page indicators */}
        <View style={onboardingStyles.indicatorContainer}>
          {onboardingData.map((_, dotIndex) => (
            <TouchableOpacity
              key={dotIndex}
              style={[
                onboardingStyles.indicator,
                dotIndex === currentIndex ? onboardingStyles.activeIndicator : onboardingStyles.inactiveIndicator,
              ]}
              onPress={() => goToSlide(dotIndex)}
            />
          ))}
        </View>
        
        {/* Dismiss/Get Started button */}
        <TouchableOpacity style={onboardingStyles.ctaContainer} onPress={handleDismiss}>
          <Text style={onboardingStyles.ctaText}>
            {isLastSlide ? 'Get Started' : 'Dismiss'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default OnboardingScreen;
