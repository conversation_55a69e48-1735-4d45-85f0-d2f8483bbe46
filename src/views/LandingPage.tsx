/**
 * Landing Page View - MVP Rewards design with dynamic content
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Alert,
  Animated,
  Dimensions,
  Image,
  Platform,
} from 'react-native';
import { normalizeFontWeight, getPlatformStyle, getPlatformImageUrl } from '../utils/styleUtils';
import FastImage from 'react-native-fast-image';
import LottieView from 'lottie-react-native';
import { colors } from '../styles/styles';
import { landingPageStyles } from '../styles/screens/LandingPageStyles';
import { SimpleNavigation } from '../controllers/AppController';
import { useLocalization } from '../locales/LocalizationService';
import { landingPageApiService } from '../services/LandingPageApiService';
import { onboardingApiService } from '../services/OnboardingApiService';
import { LandingPageApiResponse, RetryState, StyleConfig } from '../models/LandingPageModels';

interface LandingPageProps {
  navigation: SimpleNavigation;
}

const LandingPage: React.FC<LandingPageProps> = ({ navigation }) => {
  const { currentLanguage, t } = useLocalization();
  
  // Get status bar height for iOS (typically 44 for iPhone X+ with notch, 20 for older iPhones)
  const statusBarHeight = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;
  
  const [landingData, setLandingData] = useState<LandingPageApiResponse | null>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isGuestLoading, setIsGuestLoading] = useState(false);
  const [isJoinNowLoading, setIsJoinNowLoading] = useState(false);
  const [isSignInLoading, setIsSignInLoading] = useState(false);
  const [retryState, setRetryState] = useState<RetryState>({
    attempts: 0,
    maxAttempts: 3,
    isRetrying: false,
  });

  // Animation for fade-in effect
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // ...existing code...

  // Fetch landing page data
  const fetchLandingData = async () => {
    try {
      const data = await landingPageApiService.getLandingPageData(currentLanguage);
      setLandingData(data);
      setRetryState(prev => ({ ...prev, attempts: 0, isRetrying: false }));
      
      // Fade in the content when data is loaded
      if (isInitialLoad) {
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
        setIsInitialLoad(false);
      }
    } catch (error) {
      console.error('Failed to fetch landing data:', error);
      
      // Show detailed error for debugging
      let errorMessage = 'Unknown error';
      let errorDetails = '';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        errorDetails = `Name: ${error.name}\nStack: ${error.stack || 'No stack'}`;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else {
        errorMessage = String(error);
      }
      
      // Show detailed error in alert for debugging
      Alert.alert(
        `Network Error (${Platform.OS})`,
        `Message: ${errorMessage}\n\nDetails: ${errorDetails}`,
        [
          { text: 'Retry', onPress: () => {
            setRetryState({ attempts: 0, maxAttempts: 3, isRetrying: false });
            fetchLandingData();
          }},
          { text: 'Continue Offline', onPress: () => {
            // Show some default content for debugging
            console.log('Continuing in offline mode');
          }}
        ]
      );
      
      if (retryState.attempts < retryState.maxAttempts) {
        setRetryState(prev => ({
          ...prev,
          attempts: prev.attempts + 1,
          isRetrying: true,
        }));
      }
    }
  };

  // Retry mechanism
  useEffect(() => {
    if (retryState.isRetrying && retryState.attempts > 0) {
      const retryTimeout = setTimeout(() => {
        fetchLandingData();
      }, 2000 * retryState.attempts); // Exponential backoff

      return () => clearTimeout(retryTimeout);
    }
  }, [retryState.isRetrying, retryState.attempts]);

  // Initial data fetch - check cache first
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Check if data is already cached (pre-fetched by splash screen)
        const cachedData = await landingPageApiService.getValidCachedData(currentLanguage);
        if (cachedData) {
          setLandingData(cachedData);
          // Fade in immediately if we have cached data
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }).start();
          setIsInitialLoad(false);
          return;
        }
      } catch (error) {
        console.log('No cached data found, fetching fresh data');
      }
      
      // If no cached data, fetch fresh data
      fetchLandingData();
    };
    
    initializeData();
  }, [currentLanguage]);

  // Button handlers
  const handleSignIn = async () => {
    try {
      setIsSignInLoading(true);
      
      // Show dancing dots for 500ms before showing alert
      await new Promise(resolve => setTimeout(resolve, 500));
      
      Alert.alert('Sign In', 'Sign In button pressed');
    } finally {
      setIsSignInLoading(false);
    }
  };

  const handleJoinNow = async () => {
    try {
      setIsJoinNowLoading(true);
      
      // Show dancing dots for 500ms before showing alert
      await new Promise(resolve => setTimeout(resolve, 500));
      
      Alert.alert('Join Now', 'Join Now button pressed');
    } finally {
      setIsJoinNowLoading(false);
    }
  };

  const handleContinueAsGuest = async () => {
    try {
      setIsGuestLoading(true);
      
      // Show dancing dots for at least 500ms even if cache exists
      const minDelay = new Promise(resolve => setTimeout(resolve, 500));
      
      // Pre-fetch onboarding data to ensure it's ready
      const dataPromise = onboardingApiService.getOnboardingData();
      
      // Wait for both the minimum delay and data fetching to complete
      await Promise.all([minDelay, dataPromise]);
      
      // Navigate to onboarding screen
      navigation.navigate('OnboardingScreen');
    } catch (error) {
      console.error('Error loading onboarding data:', error);
      Alert.alert(
        'Error',
        'Failed to load onboarding content. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGuestLoading(false);
    }
  };

  // Render skeleton loading state for smooth UX
  const renderSkeletonContent = () => {
    return (
      <View style={landingPageStyles.mainContainer}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor="transparent"
          translucent={true}
        />
        
        {/* iOS-specific status bar background */}
        {Platform.OS === 'ios' && (
          <View style={[landingPageStyles.iosStatusBarBackground, { height: statusBarHeight }]} />
        )}
        
        <ScrollView 
          style={landingPageStyles.container} 
          contentContainerStyle={[landingPageStyles.contentContainer, landingPageStyles.contentContainerOverride]}
        >
          {/* iOS-specific content spacer to prevent overlap */}
          {Platform.OS === 'ios' && (
            <View style={[landingPageStyles.iosContentSpacer, { height: statusBarHeight }]} />
          )}
          
          {/* Skeleton Main Image */}
         <View style={[landingPageStyles.mainImage, landingPageStyles.skeletonMainImage]} />

          {/* Content with padding */}
          <View style={landingPageStyles.paddedContent}>
            
            {/* MVP icon and headline container - matching real design structure */}
            <View style={landingPageStyles.mvpHeadlineContainer}>
              <FastImage
                source={require('../assets/ic_mvp_rewards.png')}
                style={[landingPageStyles.mvpIcon, landingPageStyles.skeletonMvpIcon]}
                resizeMode={FastImage.resizeMode.contain}
              />
              
              {/* Skeleton Headline directly below MVP icon */}
              <View style={[landingPageStyles.headline, landingPageStyles.skeletonHeadline]} />
            </View>

            {/* Skeleton Sub Headline */}
            <View style={[landingPageStyles.subHeadline, landingPageStyles.skeletonSubHeadline]} />

            {/* Skeleton Buttons */}
            <View style={landingPageStyles.buttonsContainer}>
              <View style={[landingPageStyles.primaryButton, landingPageStyles.skeletonPrimaryButton]}>
                <View style={landingPageStyles.skeletonButtonText} />
              </View>
              <View style={[landingPageStyles.secondaryButton, landingPageStyles.skeletonSecondaryButton]}>
                <View style={landingPageStyles.skeletonButtonTextSecondary} />
              </View>
              <View style={[landingPageStyles.linkButton, landingPageStyles.skeletonLinkButton]}>
                <View style={landingPageStyles.skeletonLinkText} />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  };

  // If no data yet, show skeleton
  if (!landingData) {
    return renderSkeletonContent();
  }

  const config = landingData.launchScreenConfiguration;
  const mainImageUrl = getPlatformImageUrl(config.image);
  
  // Get styles from API with safety checks
  const headlineStyle = getPlatformStyle(config.headline?.style || {});
  const subHeadlineStyle = getPlatformStyle(config.subHeadline?.style || {});
  
  // Get headline size from platform-specific configuration
  const headlineSize = config?.headline?.size?.[Platform.OS as 'ios' | 'android'] || headlineStyle.fontSize || 24;
  
  // Get CTA styles from the nested structure with proper Subway brand colors
  const cta1Color = config?.CTA?.CTA1?.color || colors.subwayGreen;
  const cta1Size = config?.CTA?.CTA1?.size?.[Platform.OS as 'ios' | 'android'] || 18;
  const cta2Color = config?.CTA?.CTA2?.color || colors.white;
  const cta2Size = config?.CTA?.CTA2?.size?.[Platform.OS as 'ios' | 'android'] || 18;
  const cta3Color = config?.CTA?.CTA3?.color || colors.subwayGreen;
  const cta3Size = config?.CTA?.CTA3?.size?.[Platform.OS as 'ios' | 'android'] || 14;

  return (
    <Animated.View style={[landingPageStyles.animatedContainer, { opacity: fadeAnim }]}>
      <View style={landingPageStyles.mainContainer}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor="transparent"
          translucent={true}
        />
        
        {/* iOS-specific status bar background */}
        {Platform.OS === 'ios' && (
          <View style={[landingPageStyles.iosStatusBarBackground, { height: statusBarHeight }]} />
        )}
        
        <ScrollView 
          style={landingPageStyles.container} 
          contentContainerStyle={[
            landingPageStyles.contentContainer, 
            landingPageStyles.contentContainerOverride
          ]}
        >
          {/* iOS-specific content spacer to prevent overlap */}
          {Platform.OS === 'ios' && (
            <View style={[landingPageStyles.iosContentSpacer, { height: statusBarHeight }]} />
          )}

      {/* Main Image - Full Width */}
      {mainImageUrl && (
        <FastImage
          source={{ 
            uri: mainImageUrl,
            priority: FastImage.priority.high,
          }}
          style={landingPageStyles.mainImage}
          resizeMode={FastImage.resizeMode.cover}
        />
      )}

      {/* Content with padding - includes MVP icon and text */}
      <View style={landingPageStyles.paddedContent}>
        
        {/* MVP icon and headline container - no spacing */}
        <View style={landingPageStyles.mvpHeadlineContainer}>
          <FastImage
            source={require('../assets/ic_mvp_rewards.png')}
            style={landingPageStyles.mvpIcon}
            resizeMode={FastImage.resizeMode.contain}
          />
          
          {/* Headline directly below MVP icon */}
          <Text 
            style={[
              landingPageStyles.headline,
              {
                color: headlineStyle.color || colors.text,
                fontSize: headlineSize,
                fontWeight: normalizeFontWeight(headlineStyle.fontWeight) || 'bold',
                textAlign: headlineStyle.textAlign as any || 'center',
              }
            ]}
          >
            {config.headline?.displayText || t('landing.welcome')}
          </Text>
        </View>

      {/* Sub Headline */}
      <Text 
        style={[
          landingPageStyles.subHeadline,
          {
            color: subHeadlineStyle.color || colors.textSecondary,
            fontSize: subHeadlineStyle.fontSize || 16,
            fontWeight: normalizeFontWeight(subHeadlineStyle.fontWeight) || 'normal',
            textAlign: subHeadlineStyle.textAlign as any || 'center',
          }
        ]}
      >
        {config.subHeadline?.displayText || t('landing.tagline')}
      </Text>

      {/* Buttons Container */}
      <View style={landingPageStyles.buttonsContainer}>
        {/* Join Now Button (CTA2) - Primary Green Button */}
        <TouchableOpacity 
          style={[
            landingPageStyles.primaryButton,
            { backgroundColor: colors.subwayGreen },
            isJoinNowLoading && landingPageStyles.buttonLoadingState
          ]} 
          onPress={handleJoinNow}
          disabled={isJoinNowLoading}
        >
          {isJoinNowLoading && (
            <View style={landingPageStyles.loadingOverlay}>
              <LottieView
                source={require('../assets/dancing_dots_white_lottie.json')}
                autoPlay
                loop
                style={landingPageStyles.lottieAnimation}
              />
            </View>
          )}
          <Text 
            style={[
              landingPageStyles.primaryButtonText,
              {
                color: cta2Color,
                fontSize: cta2Size,
                fontWeight: 'bold',
                opacity: isJoinNowLoading ? 0 : 1,
              }
            ]}
          >
            {config.CTA?.CTA2?.displayText || t('landing.getStarted')}
          </Text>
        </TouchableOpacity>

        {/* Sign In Button (CTA1) - Outlined Button */}
        <TouchableOpacity 
          style={[
            landingPageStyles.secondaryButton,
            { 
              borderColor: cta1Color,
              backgroundColor: 'transparent'
            },
            isSignInLoading && landingPageStyles.buttonLoadingState
          ]} 
          onPress={handleSignIn}
          disabled={isSignInLoading}
        >
          {isSignInLoading && (
            <View style={landingPageStyles.loadingOverlay}>
              <LottieView
                source={require('../assets/dancing_dots_green_lottie.json')}
                autoPlay
                loop
                style={landingPageStyles.lottieAnimation}
              />
            </View>
          )}
          <Text 
            style={[
              landingPageStyles.secondaryButtonText,
              {
                color: cta1Color,
                fontSize: cta1Size,
                fontWeight: 'normal',
                opacity: isSignInLoading ? 0 : 1,
              }
            ]}
          >
            {config.CTA?.CTA1?.displayText || t('common.login')}
          </Text>
        </TouchableOpacity>

        {/* Continue as Guest (CTA3) */}
        <TouchableOpacity 
          style={[
            landingPageStyles.linkButton,
            landingPageStyles.guestButtonOverride,
            isGuestLoading && landingPageStyles.buttonLoadingState
          ]} 
          onPress={handleContinueAsGuest}
          disabled={isGuestLoading}
        >
          <View style={landingPageStyles.guestButtonContainer}>
            {isGuestLoading ? (
              <LottieView
                source={require('../assets/dancing_dots_green_lottie.json')}
                autoPlay
                loop
                style={landingPageStyles.lottieAnimation}
              />
            ) : (
              <Text 
                style={[
                  landingPageStyles.linkButtonText,
                  {
                    color: cta3Color,
                    fontSize: cta3Size,
                    fontWeight: 'bold',
                    textDecorationLine: 'underline',
                  }
                ]}
              >
                {config.CTA?.CTA3?.displayText || t('landing.continueAsGuest')}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </View>
      </View>
    </ScrollView>
    </View>
    
    </Animated.View>
  );
};

export default LandingPage;
