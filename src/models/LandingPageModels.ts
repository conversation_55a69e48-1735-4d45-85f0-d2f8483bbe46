/**
 * Models for Landing Page Configuration API
 */

export interface StyleConfig {
  color?: string;
  fontSize?: number;
  fontWeight?: string;
  textAlign?: string;
}

export interface PlatformStyleConfig {
  android?: StyleConfig;
  ios?: StyleConfig;
}

export interface DisplayTextConfig {
  displayText: string;
  style?: PlatformStyleConfig;
  size?: {
    android: number;
    ios: number;
  };
}

export interface ImageConfig {
  android?: {
    url: string;
    width?: number;
    height?: number;
  };
  ios?: {
    url: string;
    width?: number;
    height?: number;
  };
}

export interface CTAButtonConfig {
  displayText: string;
  color: string;
  size: {
    android: number;
    ios: number;
  };
}

export interface CTAConfig {
  CTA1: CTAButtonConfig;
  CTA2: CTAButtonConfig;
  CTA3: CTAButtonConfig;
}

export interface LaunchScreenConfiguration {
  headline: DisplayTextConfig;
  subHeadline: DisplayTextConfig;
  image: ImageConfig;
  CTA: CTAConfig;
  legalDisclaimer?: DisplayTextConfig;
}

export interface LandingPageApiResponse {
  launchScreenConfiguration: LaunchScreenConfiguration;
}

export interface CachedLandingData {
  data: LandingPageApiResponse;
  lastModified: string;
  timestamp: number;
  language: string;
}

export interface RetryState {
  attempts: number;
  maxAttempts: number;
  isRetrying: boolean;
}
