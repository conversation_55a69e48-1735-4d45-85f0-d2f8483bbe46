/**
 * App Model - Handles application state and data management
 */

export interface UserData {
  id?: string;
  name?: string;
  email?: string;
  isLoggedIn: boolean;
}

export interface AppState {
  isLoading: boolean;
  user: UserData;
  currentScreen: string;
}

export class AppModel {
  private state: AppState;
  private observers: Array<(state: AppState) => void> = [];

  constructor() {
    this.state = {
      isLoading: true,
      user: {
        isLoggedIn: false,
      },
      currentScreen: 'SplashScreen',
    };
  }

  // Observer pattern for state management
  subscribe(observer: (state: AppState) => void): () => void {
    this.observers.push(observer);
    return () => {
      this.observers = this.observers.filter(obs => obs !== observer);
    };
  }

  private notifyObservers(): void {
    this.observers.forEach(observer => observer(this.state));
  }

  // State getters
  getState(): AppState {
    return { ...this.state };
  }

  getCurrentScreen(): string {
    return this.state.currentScreen;
  }

  isLoading(): boolean {
    return this.state.isLoading;
  }

  getUser(): UserData {
    return { ...this.state.user };
  }

  // State setters
  setLoading(loading: boolean): void {
    this.state = {
      ...this.state,
      isLoading: loading,
    };
    this.notifyObservers();
  }

  setCurrentScreen(screen: string): void {
    this.state = {
      ...this.state,
      currentScreen: screen,
    };
    this.notifyObservers();
  }

  setUser(user: UserData): void {
    this.state = {
      ...this.state,
      user: { ...user },
    };
    this.notifyObservers();
  }

  // Business logic methods
  async initializeApp(): Promise<void> {
    // Simulate app initialization (loading user data, checking auth, etc.)
    return new Promise(resolve => {
      setTimeout(() => {
        this.setLoading(false);
        this.setCurrentScreen('LandingPage');
        resolve();
      }, 3000); // 3 second splash screen
    });
  }

  async loginUser(email: string, password: string): Promise<boolean> {
    // Simulate login logic
    this.setLoading(true);
    
    return new Promise(resolve => {
      setTimeout(() => {
        const success = email.length > 0 && password.length > 0;
        
        if (success) {
          this.setUser({
            id: '1',
            name: 'John Doe',
            email: email,
            isLoggedIn: true,
          });
        }
        
        this.setLoading(false);
        resolve(success);
      }, 1500);
    });
  }

  logoutUser(): void {
    this.setUser({
      isLoggedIn: false,
    });
  }
}

// Singleton instance
export const appModel = new AppModel();
