import React, { useEffect } from 'react';
import { Alert, Text, View } from 'react-native';
import BuildConfigModule from '../config/NativeBuildConfig';
import { environmentManager } from '../config/EnvironmentConfig';

export const TestDebugComponent: React.FC = () => {
  useEffect(() => {
    // Test the configuration on mount
    setTimeout(() => {
      const nativeValues = {
        ENVIRONMENT: BuildConfigModule.ENVIRONMENT,
        IS_PRODUCTION_BUILD: BuildConfigModule.IS_PRODUCTION_BUILD,
        BUNDLE_ID: BuildConfigModule.BUNDLE_ID,
      };

      const currentEnv = environmentManager.getCurrentEnvironment();

      // Log to console
      console.log('=== CONFIGURATION TEST ===');
      console.log('Native Module Values:', nativeValues);
      console.log('Environment Manager Values:', {
        name: currentEnv.name,
        isProduction: currentEnv.isProduction,
        displayName: currentEnv.displayName,
      });
      console.log('=== END CONFIGURATION TEST ===');

      Alert.alert(
        'Configuration Test',
        `Native Module:
ENVIRONMENT: ${nativeValues.ENVIRONMENT}
IS_PRODUCTION_BUILD: ${nativeValues.IS_PRODUCTION_BUILD}
BUNDLE_ID: ${nativeValues.BUNDLE_ID}

Environment Manager:
Current Env: ${currentEnv.name}
Is Production: ${currentEnv.isProduction}
Display Name: ${currentEnv.displayName}`,
        [{ text: 'OK' }]
      );
    }, 1000);
  }, []);

  return (
    <View style={{ padding: 20, backgroundColor: 'yellow', position: 'absolute', top: 100, left: 10, right: 10, zIndex: 999 }}>
      <Text style={{ color: 'black', fontWeight: 'bold' }}>Configuration Test Component</Text>
      <Text style={{ color: 'black' }}>Bundle ID: {BuildConfigModule.BUNDLE_ID}</Text>
      <Text style={{ color: 'black' }}>Environment: {BuildConfigModule.ENVIRONMENT}</Text>
      <Text style={{ color: 'black' }}>Is Production: {BuildConfigModule.IS_PRODUCTION_BUILD?.toString()}</Text>
    </View>
  );
};
