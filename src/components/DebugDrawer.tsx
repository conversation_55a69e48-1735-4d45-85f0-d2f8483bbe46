/**
 * Debug Drawer Component
 * Shows build information and e  useEffect(() => {
    // Fetch bundle ID
    try {
      const appBundleId = DeviceInfo.getBundleId();
      setBundleId(appBundleId);
    } catch (error) {
      console.warn('Failed to get bundle ID:', error);
      setBundleId('Error: Unable to fetch bundle ID');
    }
  }, []); switching for non-production builds
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
  Alert,
  Platform,
  PixelRatio,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { environmentManager, EnvironmentConfig } from '../config/EnvironmentConfig';
import { buildInfoService } from '../utils/BuildInfo';
import { landingPageApiService } from '../services/LandingPageApiService';

interface DebugDrawerProps {
  isVisible: boolean;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');
const DRAWER_WIDTH = width * 0.85;

export const DebugDrawer: React.FC<DebugDrawerProps> = ({ isVisible, onClose }) => {
  const [slideAnim] = useState(new Animated.Value(DRAWER_WIDTH));
  const [currentEnvironment, setCurrentEnvironment] = useState<EnvironmentConfig>(
    environmentManager.getCurrentEnvironment()
  );
  const [environments, setEnvironments] = useState<EnvironmentConfig[]>([]);
  const [bundleId, setBundleId] = useState<string>('Loading...');
  const [buildInfo] = useState(() => buildInfoService.getBuildInfo());
  const [deviceInfo] = useState(() => {
    const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');
    const pixelRatio = PixelRatio.get();
    const density = Math.round(pixelRatio * 160);
    
    return {
      make: Platform.OS === 'ios' ? 'Apple' : 'Google',
      model: Platform.OS === 'ios' ? 'iPhone' : 'Pixel 9',
      resolution: `${Math.round(screenWidth)} x ${Math.round(screenHeight)}`,
      density: `${density}dpi (${pixelRatio})`,
      release: Platform.Version.toString(),
      api: Platform.OS === 'ios' ? '17' : '35',
    };
  });

  useEffect(() => {
    const fetchEnvs = async () => {
      // Get environments available for the current build type
      const availableEnvironments = await environmentManager.getAllEnvironments();
      setEnvironments(availableEnvironments);
    };
    fetchEnvs();
  }, []);

  useEffect(() => {
    // Fetch bundle ID
    try {
      const appBundleId = DeviceInfo.getBundleId();
      setBundleId(appBundleId);
    } catch (error) {
      console.warn('Failed to get bundle ID:', error);
      setBundleId('Error fetching bundle ID');
    }
  }, []);

  useEffect(() => {
    if (isVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: DRAWER_WIDTH,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, slideAnim]);

  const handleEnvironmentSwitch = async (environment: EnvironmentConfig) => {
    if (environment.name === currentEnvironment.name) {
      return;
    }

    Alert.alert(
      'Switch Environment',
      `Are you sure you want to switch to ${environment.displayName}? This will restart the app.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Switch',
          onPress: async () => {
            const success = await environmentManager.switchEnvironment(environment.name);
            if (success) {
              setCurrentEnvironment(environment);
              onClose();
            }
          },
        },
      ]
    );
  };

  const handleClose = () => {
    Animated.timing(slideAnim, {
      toValue: DRAWER_WIDTH,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.backdrop} onPress={handleClose} />
        
        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{ translateX: slideAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Debug Menu</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* App Information */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>App Information</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Build</Text>
                <Text style={styles.infoValue}>{buildInfo.buildNumber}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Build Type</Text>
                <Text style={styles.infoValue}>debug</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Bundle ID</Text>
                <Text style={styles.infoValue}>{bundleId}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Environment</Text>
                <Text style={styles.infoValue}>{currentEnvironment.displayName}</Text>
              </View>
            </View>

            {/* Environment Switcher */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Switch Environment</Text>
              {environments.map((env) => (
                <TouchableOpacity
                  key={env.name}
                  style={[
                    styles.environmentOption,
                    env.name === currentEnvironment.name && styles.environmentOptionSelected,
                  ]}
                  onPress={() => handleEnvironmentSwitch(env)}
                >
                  <View style={styles.environmentOptionContent}>
                    <Text
                      style={[
                        styles.environmentOptionName,
                        env.name === currentEnvironment.name && styles.environmentOptionNameSelected,
                      ]}
                    >
                      {env.displayName}
                    </Text>
                    <Text
                      style={[
                        styles.environmentOptionUrl,
                        env.name === currentEnvironment.name && styles.environmentOptionUrlSelected,
                      ]}
                    >
                      {env.apiBaseUrl}
                    </Text>
                  </View>
                  {env.name === currentEnvironment.name && (
                    <Text style={styles.selectedIndicator}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Device Information */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Device Information</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Make</Text>
                <Text style={styles.infoValue}>{deviceInfo.make}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Model</Text>
                <Text style={styles.infoValue}>{deviceInfo.model}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Resolution</Text>
                <Text style={styles.infoValue}>{deviceInfo.resolution}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Density</Text>
                <Text style={styles.infoValue}>{deviceInfo.density}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Release</Text>
                <Text style={styles.infoValue}>{deviceInfo.release}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>API</Text>
                <Text style={styles.infoValue}>{deviceInfo.api}</Text>
              </View>
            </View>

            {/* Clear Cache Button */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.clearCacheButton}
                onPress={() => {
                  Alert.alert(
                    'Clear Cache',
                    'This will clear all cached data. Continue?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Clear',
                        onPress: async () => {
                          try {
                            await landingPageApiService.clearCache();
                            Alert.alert('Cache Cleared', 'All cached data has been cleared.');
                          } catch (error) {
                            Alert.alert('Error', 'Failed to clear cache.');
                          }
                        },
                      },
                    ]
                  );
                }}
              >
                <Text style={styles.clearCacheButtonText}>Clear Cache</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = {
  overlay: {
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawer: {
    width: DRAWER_WIDTH,
    height: height,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    padding: 16,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 16 : 50,
    backgroundColor: '#4CAF50',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: '#ffffff',
  },
  closeButton: {
    width: 32,
    height: 32,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '500' as const,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#333333',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  infoRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500' as const,
    flex: 1,
    textAlign: 'right' as const,
  },
  environmentOption: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 8,
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  environmentOptionSelected: {
    borderColor: '#4CAF50',
    backgroundColor: '#f0f8f0',
  },
  environmentOptionContent: {
    flex: 1,
  },
  environmentOptionName: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#333333',
    marginBottom: 4,
  },
  environmentOptionNameSelected: {
    color: '#4CAF50',
    fontWeight: '600' as const,
  },
  environmentOptionUrl: {
    fontSize: 12,
    color: '#666666',
    lineHeight: 16,
  },
  environmentOptionUrlSelected: {
    color: '#2E7D32',
  },
  selectedIndicator: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '600' as const,
  },
  clearCacheButton: {
    padding: 16,
    backgroundColor: '#f44336',
    borderRadius: 8,
    alignItems: 'center' as const,
    marginTop: 16,
  },
  clearCacheButtonText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#ffffff',
  },
};
