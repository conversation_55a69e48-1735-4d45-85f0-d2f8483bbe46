/**
 * Environment Debug Component
 * Shows the current environment configuration for debugging
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { environmentManager } from '../config/EnvironmentConfig';

const EnvironmentDebug: React.FC = async () => {
  const currentEnv = environmentManager.getCurrentEnvironment();
  const isLowerBuild = !environmentManager.isProduction();
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Environment Debug Info</Text>
      <Text style={styles.info}>Environment: {currentEnv.name}</Text>
      <Text style={styles.info}>Display Name: {currentEnv.displayName}</Text>
      <Text style={styles.info}>API Base URL: {currentEnv.apiBaseUrl}</Text>
      <Text style={styles.info}>Is Production: {currentEnv.isProduction ? 'Yes' : 'No'}</Text>
      <Text style={styles.info}>Is Lower Build: {isLowerBuild ? 'Yes' : 'No'}</Text>
      <Text style={styles.info}>Debug Drawer Enabled: {environmentManager.isDebugDrawerEnabled() ? 'Yes' : 'No'}</Text>
      <Text style={styles.info}>Environment Switching: {await environmentManager.isEnvironmentSwitchingAllowed() ? 'Allowed' : 'Not Allowed'}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f0f0f0',
    margin: 10,
    borderRadius: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  info: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
});

export default EnvironmentDebug;
