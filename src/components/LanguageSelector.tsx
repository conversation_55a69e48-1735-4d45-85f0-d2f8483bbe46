/**
 * Language Selector Component - Allows users to change app language
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { useLocalization } from '../locales/LocalizationService';
import { colors } from '../styles/styles';

interface LanguageSelectorProps {
  visible: boolean;
  onClose: () => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ visible, onClose }) => {
  const { t, currentLanguage, availableLanguages, changeLanguage, getLanguageDisplayName } = useLocalization();

  const handleLanguageSelect = async (language: string) => {
    await changeLanguage(language);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>Select Language</Text>
          
          <ScrollView style={styles.languageList}>
            {availableLanguages.map((language) => (
              <TouchableOpacity
                key={language}
                style={[
                  styles.languageItem,
                  currentLanguage === language && styles.selectedLanguage,
                ]}
                onPress={() => handleLanguageSelect(language)}
              >
                <Text
                  style={[
                    styles.languageText,
                    currentLanguage === language && styles.selectedLanguageText,
                  ]}
                >
                  {getLanguageDisplayName(language)}
                </Text>
                {currentLanguage === language && (
                  <Text style={styles.checkmark}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxWidth: 400,
    maxHeight: '70%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: colors.darkGray,
  },
  languageList: {
    maxHeight: 300,
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    backgroundColor: colors.lightGray,
  },
  selectedLanguage: {
    backgroundColor: colors.primary,
  },
  languageText: {
    fontSize: 16,
    color: colors.darkGray,
  },
  selectedLanguageText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  checkmark: {
    fontSize: 18,
    color: colors.white,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: colors.secondary,
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.darkGray,
  },
});

export default LanguageSelector;
