/**
 * Debug <PERSON>u Handler
 * Provides swipe gesture to open the debug drawer (non-production builds only)
 */

import React, { useState } from 'react';
import { View, PanResponder, Dimensions, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { environmentManager } from '../config/EnvironmentConfig';
import { DebugDrawer } from './DebugDrawer';

interface DebugMenuHandlerProps {
  children: React.ReactNode;
}

const { width } = Dimensions.get('window');
const SWIPE_THRESHOLD = 50;
const EDGE_THRESHOLD = 50;

export const DebugMenuHandler: React.FC<DebugMenuHandlerProps> = ({ children }) => {
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);

  const isDebugEnabled = environmentManager.isDebugDrawerEnabled();

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: (evt) => {
      if (!isDebugEnabled) return false;
      
      const { pageX } = evt.nativeEvent;
      const isRightEdge = pageX > width - EDGE_THRESHOLD;
      
      return isRightEdge;
    },
    
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      if (!isDebugEnabled) return false;
      
      const { dx } = gestureState;
      const { pageX } = evt.nativeEvent;
      const isSwipingLeft = dx < -5;
      const isFromRightEdge = pageX > width - EDGE_THRESHOLD;
      
      return isFromRightEdge && isSwipingLeft;
    },
    
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, vx } = gestureState;
      const { pageX } = evt.nativeEvent;
      
      const isValidSwipe = (dx < -SWIPE_THRESHOLD || vx < -0.5) && pageX > width - EDGE_THRESHOLD;
      
      if (isValidSwipe) {
        setIsDrawerVisible(true);
      }
    },
  });

  if (!isDebugEnabled) {
    return <>{children}</>;
  }

  return (
    <View style={{ flex: 1 }} {...panResponder.panHandlers}>
      {children}
      
      {/* Debug drawer - only in non-production */}
      {isDebugEnabled && (
        <>
          {/* Debug button */}
          <TouchableOpacity
            style={styles.debugButton}
            onPress={() => setIsDrawerVisible(true)}
            activeOpacity={0.7}
          >
            <Text style={styles.debugIcon}>🛠️</Text>
          </TouchableOpacity>
          
          <DebugDrawer
            isVisible={isDrawerVisible}
            onClose={() => setIsDrawerVisible(false)}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  debugButton: {
    position: 'absolute',
    top: 100,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  debugIcon: {
    fontSize: 18,
    textAlign: 'center',
  },
});
