/**
 * Main App Entry Point with MVC Architecture
 */

export { default as AppNavigator } from './navigation/AppNavigator';
export { appModel, AppModel } from './models/AppModel';
export { appController, AppController } from './controllers/AppController';
export { default as SplashScreen } from './views/SplashScreen';
export { default as LandingPage } from './views/LandingPage';
export { default as LanguageSelector } from './components/LanguageSelector';
export { useLocalization, localizationService } from './locales/LocalizationService';
export { environmentManager } from './config/EnvironmentConfig';
export { buildInfoService } from './utils/BuildInfo';
export { DebugDrawer } from './components/DebugDrawer';
export * from './styles/styles';
