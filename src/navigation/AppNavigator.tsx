/**
 * Simple Navigation Setup - Custom navigation without react-navigation dependencies
 */

import React, { useState, useEffect } from 'react';
import { View } from 'react-native';

// Import screens
import SplashScreen from '../views/SplashScreen';
import LandingPage from '../views/LandingPage';
import OnboardingScreen from '../views/OnboardingScreen';

// Import controller to listen to navigation changes
import { appController } from '../controllers/AppController';

export type Screen = 'SplashScreen' | 'LandingPage' | 'OnboardingScreen';

interface NavigationProps {
  navigate: (screen: Screen) => void;
  currentScreen: Screen;
}

const AppNavigator: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('SplashScreen');

  useEffect(() => {
    // Subscribe to app state changes to handle navigation
    const unsubscribe = appController.subscribeToStateChanges((state) => {
      if (state.currentScreen !== currentScreen) {
        setCurrentScreen(state.currentScreen as Screen);
      }
    });

    return unsubscribe;
  }, [currentScreen]);

  const navigate = (screen: Screen) => {
    setCurrentScreen(screen);
    appController.setCurrentScreen(screen);
  };

  const navigationProps: NavigationProps = {
    navigate,
    currentScreen,
  };

  const renderScreen = () => {
    switch (currentScreen) {
      case 'SplashScreen':
        return <SplashScreen navigation={navigationProps} />;
      case 'LandingPage':
        return <LandingPage navigation={navigationProps} />;
      case 'OnboardingScreen':
        return <OnboardingScreen navigation={navigationProps} />;
      default:
        return <SplashScreen navigation={navigationProps} />;
    }
  };

  return (
    <View style={{ flex: 1 }}>{renderScreen()}</View>
  );
};

export default AppNavigator;
