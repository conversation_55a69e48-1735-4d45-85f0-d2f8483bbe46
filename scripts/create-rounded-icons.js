#!/usr/bin/env node

/**
 * <PERSON>ript to create properly rounded Android icons using Sharp (Node.js)
 */

async function createRoundedIcons() {
  try {
    const sharp = require('sharp');
    const fs = require('fs');
    const path = require('path');

    // Icon sizes for different Android densities
    const sizes = {
      'mipmap-mdpi': 48,
      'mipmap-hdpi': 72,
      'mipmap-xhdpi': 96,
      'mipmap-xxhdpi': 144,
      'mipmap-xxxhdpi': 192
    };

    const inputImage = 'src/assets/subway-app-icon.png';

    if (!fs.existsSync(inputImage)) {
      console.log(`❌ Input image not found: ${inputImage}`);
      process.exit(1);
    }

    console.log('🎨 Creating rounded Android icons...');

    for (const [density, size] of Object.entries(sizes)) {
      const outputPath = `android/app/src/main/res/${density}/ic_launcher_round.png`;
      const outputDir = path.dirname(outputPath);
      
      // Ensure directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // Create a circular mask
      const circleSvg = `
        <svg width="${size}" height="${size}">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="white"/>
        </svg>
      `;

      await sharp(inputImage)
        .resize(size, size)
        .composite([{
          input: Buffer.from(circleSvg),
          blend: 'dest-in'
        }])
        .png()
        .toFile(outputPath);

      console.log(`✅ Created rounded icon: ${outputPath}`);
    }

    console.log('🎉 All rounded icons created successfully!');

  } catch (error) {
    if (error.code === 'MODULE_NOT_FOUND') {
      console.log('❌ Sharp is required to create rounded icons.');
      console.log('📦 Install it with: npm install sharp');
      process.exit(1);
    } else {
      console.log(`❌ Error creating rounded icons: ${error.message}`);
      process.exit(1);
    }
  }
}

createRoundedIcons();
