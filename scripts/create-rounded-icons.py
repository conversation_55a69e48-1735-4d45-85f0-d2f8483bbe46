#!/usr/bin/env python3
"""
<PERSON>ript to create properly rounded Android icons
"""

try:
    from PIL import Image, ImageDraw
    import os
    
    def create_rounded_icon(input_path, output_path, size):
        """Create a rounded icon from the input image"""
        # Open and resize the original image
        img = Image.open(input_path).convert("RGBA")
        img = img.resize((size, size), Image.Resampling.LANCZOS)
        
        # Create a mask for the rounded corners
        mask = Image.new('L', (size, size), 0)
        draw = ImageDraw.Draw(mask)
        draw.ellipse((0, 0, size, size), fill=255)
        
        # Apply the mask to create rounded corners
        rounded_img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        rounded_img.paste(img, (0, 0))
        rounded_img.putalpha(mask)
        
        # Save the rounded icon
        rounded_img.save(output_path)
        print(f"✅ Created rounded icon: {output_path}")
    
    # Icon sizes for different Android densities
    sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    input_image = 'src/assets/subway-app-icon.png'
    
    if not os.path.exists(input_image):
        print(f"❌ Input image not found: {input_image}")
        exit(1)
    
    print("🎨 Creating rounded Android icons...")
    
    for density, size in sizes.items():
        output_path = f'android/app/src/main/res/{density}/ic_launcher_round.png'
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        create_rounded_icon(input_image, output_path, size)
    
    print("🎉 All rounded icons created successfully!")
    
except ImportError:
    print("❌ PIL (Pillow) is required to create rounded icons.")
    print("📦 Install it with: pip install Pillow")
    exit(1)
except Exception as e:
    print(f"❌ Error creating rounded icons: {e}")
    exit(1)
