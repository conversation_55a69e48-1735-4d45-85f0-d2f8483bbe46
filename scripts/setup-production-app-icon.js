const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Define paths
const sourceIcon = path.join(__dirname, '..', 'src', 'assets', 'subway-prod-app-icon.png');
const androidResPath = path.join(__dirname, '..', 'android', 'app', 'src', 'production', 'res');
const iosIconSetPath = path.join(__dirname, '..', 'ios', 'SubwayRNApp', 'Images.xcassets', 'AppIcon-Production.appiconset');

// Android icon sizes (width x height in pixels)
const androidSizes = {
  'mipmap-mdpi': 48,
  'mipmap-hdpi': 72,
  'mipmap-xhdpi': 96,
  'mipmap-xxhdpi': 144,
  'mipmap-xxxhdpi': 192
};

// iOS icon sizes (1x, 2x, 3x multipliers for different point sizes)
const iosSizes = {
  '<EMAIL>': 40,
  '<EMAIL>': 60,
  '<EMAIL>': 58,
  '<EMAIL>': 87,
  '<EMAIL>': 80,
  '<EMAIL>': 120,
  '<EMAIL>': 120,
  '<EMAIL>': 180,
  'icon-1024.png': 1024
};

console.log('🚇 Subway Production App Icon Setup');
console.log('===================================\n');

// Check if source icon exists
if (!fs.existsSync(sourceIcon)) {
  console.error('❌ Source icon not found:', sourceIcon);
  process.exit(1);
}

console.log('📱 Setting up Android Production Icons...');

// Create Android production resource directories
Object.keys(androidSizes).forEach(density => {
  const dir = path.join(androidResPath, density);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  }
});

// Generate Android icons
Object.entries(androidSizes).forEach(([density, size]) => {
  const outputDir = path.join(androidResPath, density);
  
  // Regular square icon
  const regularIcon = path.join(outputDir, 'ic_launcher.png');
  try {
    execSync(`sips -z ${size} ${size} "${sourceIcon}" --out "${regularIcon}"`, { stdio: 'pipe' });
    console.log(`✅ Generated ${density}/ic_launcher.png (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate ${density}/ic_launcher.png:`, error.message);
  }
  
  // Round icon (circular mask)
  const roundIcon = path.join(outputDir, 'ic_launcher_round.png');
  try {
    // Create a circular version using sips
    const tempSquare = path.join(outputDir, 'temp_square.png');
    execSync(`sips -z ${size} ${size} "${sourceIcon}" --out "${tempSquare}"`, { stdio: 'pipe' });
    
    // Create circular mask using ImageMagick if available, otherwise use sips with padding
    try {
      execSync(`convert "${tempSquare}" -resize ${size}x${size} \\( +clone -threshold -1 -negate -fill white -draw "circle ${size/2},${size/2} ${size/2},0" \\) -alpha off -compose copy_opacity -composite "${roundIcon}"`, { stdio: 'pipe' });
    } catch {
      // Fallback: just copy the square version (sips doesn't have good circular masking)
      execSync(`cp "${tempSquare}" "${roundIcon}"`, { stdio: 'pipe' });
    }
    
    // Clean up temp file
    if (fs.existsSync(tempSquare)) {
      fs.unlinkSync(tempSquare);
    }
    
    console.log(`✅ Generated ${density}/ic_launcher_round.png (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate ${density}/ic_launcher_round.png:`, error.message);
  }
});

console.log('\n📱 Setting up iOS Production Icons...');

// Create iOS icon set directory
if (!fs.existsSync(iosIconSetPath)) {
  fs.mkdirSync(iosIconSetPath, { recursive: true });
  console.log(`✅ Created iOS icon set directory: ${iosIconSetPath}`);
}

// Generate iOS icons
Object.entries(iosSizes).forEach(([filename, size]) => {
  const outputFile = path.join(iosIconSetPath, filename);
  try {
    execSync(`sips -z ${size} ${size} "${sourceIcon}" --out "${outputFile}"`, { stdio: 'pipe' });
    console.log(`✅ Generated ${filename} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate ${filename}:`, error.message);
  }
});

// Create iOS Contents.json for the production icon set
const contentsJson = {
  "images": [
    {
      "size": "20x20",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "2x"
    },
    {
      "size": "20x20",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "3x"
    },
    {
      "size": "29x29",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "2x"
    },
    {
      "size": "29x29",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "3x"
    },
    {
      "size": "40x40",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "2x"
    },
    {
      "size": "40x40",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "3x"
    },
    {
      "size": "60x60",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "2x"
    },
    {
      "size": "60x60",
      "idiom": "iphone",
      "filename": "<EMAIL>",
      "scale": "3x"
    },
    {
      "size": "1024x1024",
      "idiom": "ios-marketing",
      "filename": "icon-1024.png",
      "scale": "1x"
    }
  ],
  "info": {
    "version": 1,
    "author": "xcode"
  }
};

const contentsJsonPath = path.join(iosIconSetPath, 'Contents.json');
fs.writeFileSync(contentsJsonPath, JSON.stringify(contentsJson, null, 2));
console.log('✅ Created iOS Contents.json configuration');

console.log('\n🎉 SUCCESS! Production app icons generated successfully.');

console.log('\n📋 What was created:');
console.log('✅ Android production icons at 5 different resolutions');
console.log('✅ Android production round icons for circular launchers');
console.log('✅ iOS production icons at 9 different resolutions');
console.log('✅ iOS production AppIcon set configuration');

console.log('\n📱 Android Production Icons:');
Object.keys(androidSizes).forEach(density => {
  console.log(`   • android/app/src/production/res/${density}/ic_launcher.png`);
  console.log(`   • android/app/src/production/res/${density}/ic_launcher_round.png`);
});

console.log('\n🍎 iOS Production Icons:');
console.log('   • ios/SubwayRNApp/Images.xcassets/AppIcon-Production.appiconset/');

console.log('\n🚀 Next Steps:');
console.log('1. Update Android build configuration to use production-specific icons');
console.log('2. Update iOS production scheme to use the new AppIcon-Production asset');
console.log('3. Test both debug and release builds for production flavor');

console.log('\n💡 Note: Production builds will now have their own distinct icon');
console.log('   while lower builds continue to use the original icon.');
