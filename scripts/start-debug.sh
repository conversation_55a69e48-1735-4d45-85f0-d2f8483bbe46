#!/bin/bash

# Function to check if a port is available
check_port() {
    local port=$1
    if ! nc -z localhost $port; then
        return 0  # Port is available
    else
        return 1  # Port is in use
    fi
}

# Function to wait for Metro to be ready
wait_for_metro() {
    local port=$1
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for Metro to start on port $port..."
    while ! nc -z localhost $port; do
        if [ $attempt -ge $max_attempts ]; then
            echo "❌ Metro failed to start after $max_attempts attempts"
            exit 1
        fi
        sleep 1
        ((attempt++))
    done
    echo "✅ Metro is ready!"
}

echo "🧹 Cleaning up existing processes..."
pkill -f "react-native start" || true
adb reverse --remove-all || true

# Wait until port 8081 is definitely free
while ! check_port 8081; do
    echo "⏳ Waiting for port 8081 to be available..."
    sleep 1
done

# Start Metro in the background
echo "🚀 Starting Metro..."
yarn start > /tmp/metro.log 2>&1 &
METRO_PID=$!

# Wait for Metro to be ready
wait_for_metro 8081

# Set up port forwarding
echo "🔄 Setting up port forwarding..."
adb reverse tcp:8081 tcp:8081
adb reverse tcp:8097 tcp:8097

# Start the app
echo "📱 Building and starting the app..."
yarn android:lower

# Start React DevTools
echo "🛠 Starting React DevTools..."
react-devtools &

# Open Chrome DevTools
echo "🔍 Opening Chrome DevTools..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    open -a "Google Chrome" chrome://inspect
else
    xdg-open chrome://inspect
fi

echo "
✨ Debug environment ready!

📱 App is starting...
🛠 React DevTools launched
🔍 Chrome DevTools opened

To complete Chrome DevTools setup (one-time):
1. Click 'Configure...' in chrome://inspect
2. Add 'localhost:8081'
3. Wait for your app to appear under 'Remote Target'

Use Cmd+M (Mac) or Ctrl+M (Windows) in the emulator to open the Dev Menu
"

# Wait for Metro (if it dies, we should exit)
wait $METRO_PID
