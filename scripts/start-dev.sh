#!/bin/bash

# Check if platform argument is provided
if [ "$1" != "ios" ] && [ "$1" != "android" ]; then
    echo "❌ Please specify platform: ios or android"
    echo "Usage: $0 [ios|android] [--debug]"
    exit 1
fi
PLATFORM=$1
DEBUG_MODE=$2

# Function to check if a port is available
check_port() {
    local port=$1
    if ! nc -z localhost $port; then
        return 0  # Port is available
    else
        return 1  # Port is in use
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    lsof -i :$port | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null || true
}

# Function to wait for Metro to be ready
wait_for_metro() {
    local port=$1
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for Metro to start on port $port..."
    while ! nc -z localhost $port; do
        if [ $attempt -ge $max_attempts ]; then
            echo "❌ Metro failed to start after $max_attempts attempts"
            exit 1
        fi
        sleep 1
        ((attempt++))
    done
    echo "✅ Metro is ready!"
}

echo "🧹 Cleaning up existing processes..."
# Kill any existing processes
kill_port 8081
kill_port 8097
pkill -f "react-native start" || true
if [ "$PLATFORM" = "android" ]; then
    adb reverse --remove-all || true
fi

# Kill Metro if running
if pgrep -f "react-native start" > /dev/null; then
    echo "Killing existing Metro process..."
    pkill -f "react-native start"
    sleep 2
fi

# Double check port 8081
while ! check_port 8081; do
    echo "⏳ Waiting for port 8081 to be available..."
    kill_port 8081
    sleep 2
done

# Start Metro in the background
echo "🚀 Starting Metro..."
yarn start > /tmp/metro.log 2>&1 &
METRO_PID=$!

# Wait for Metro to be ready
wait_for_metro 8081

if [ "$PLATFORM" = "android" ]; then
    # Set up port forwarding for Android
    echo "🔄 Setting up port forwarding..."
    adb reverse tcp:8081 tcp:8081
    adb reverse tcp:8097 tcp:8097
    adb reverse tcp:9090 tcp:9090  # Additional debug port
    
    # Kill app process to ensure clean start
    adb shell am force-stop com.subwayrnapp.lower
    sleep 2

    # Start the Android app
    echo "📱 Building and starting Android app..."
    yarn android:lower
else
    # Start the iOS app
    echo "📱 Building and starting iOS app..."
    yarn ios:lower
fi

if [ "$DEBUG_MODE" = "--debug" ]; then
    # Start React DevTools
    echo "🛠 Starting React DevTools..."
    react-devtools &

    # Open Chrome DevTools
    echo "🔍 Opening Chrome DevTools..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open -a "Google Chrome" chrome://inspect
    else
        xdg-open chrome://inspect
    fi

    echo "
✨ Debug environment ready!

📱 App is starting...
🛠 React DevTools launched
🔍 Chrome DevTools opened

To complete Chrome DevTools setup (one-time):
1. Click 'Configure...' in chrome://inspect
2. Add 'localhost:8081'
3. Wait for your app to appear under 'Remote Target'

Dev Menu shortcuts:
- iOS Simulator: Cmd + D
- Android Emulator: Cmd + M
"
else
    echo "
✨ Development environment ready!

📱 App is starting...

Dev Menu shortcuts:
- iOS Simulator: Cmd + D
- Android Emulator: Cmd + M
"
fi

# Wait for Metro (if it dies, we should exit)
wait $METRO_PID
