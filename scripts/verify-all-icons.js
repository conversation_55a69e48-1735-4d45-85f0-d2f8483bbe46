const fs = require('fs');
const path = require('path');

console.log('🚇 Subway App Icon Verification (Regular + Production)');
console.log('=====================================================\n');

// Check regular Android icons (used by lower flavor)
console.log('📱 Verifying Regular Android Icons (Lower Flavor):');
const androidRegularSizes = ['hdpi', 'mdpi', 'xhdpi', 'xxhdpi', 'xxxhdpi'];
const androidMainResPath = path.join(__dirname, '..', 'android', 'app', 'src', 'main', 'res');

androidRegularSizes.forEach(density => {
  const regularIcon = path.join(androidMainResPath, `mipmap-${density}`, 'ic_launcher.png');
  const roundIcon = path.join(androidMainResPath, `mipmap-${density}`, 'ic_launcher_round.png');
  
  if (fs.existsSync(regularIcon)) {
    console.log(`✅ android/app/src/main/res/mipmap-${density}/ic_launcher.png`);
  } else {
    console.log(`❌ android/app/src/main/res/mipmap-${density}/ic_launcher.png`);
  }
  
  if (fs.existsSync(roundIcon)) {
    console.log(`✅ android/app/src/main/res/mipmap-${density}/ic_launcher_round.png`);
  } else {
    console.log(`❌ android/app/src/main/res/mipmap-${density}/ic_launcher_round.png`);
  }
});

// Check production Android icons
console.log('\n📱 Verifying Production Android Icons (Production Flavor):');
const androidProductionResPath = path.join(__dirname, '..', 'android', 'app', 'src', 'production', 'res');

androidRegularSizes.forEach(density => {
  const regularIcon = path.join(androidProductionResPath, `mipmap-${density}`, 'ic_launcher.png');
  const roundIcon = path.join(androidProductionResPath, `mipmap-${density}`, 'ic_launcher_round.png');
  
  if (fs.existsSync(regularIcon)) {
    console.log(`✅ android/app/src/production/res/mipmap-${density}/ic_launcher.png`);
  } else {
    console.log(`❌ android/app/src/production/res/mipmap-${density}/ic_launcher.png`);
  }
  
  if (fs.existsSync(roundIcon)) {
    console.log(`✅ android/app/src/production/res/mipmap-${density}/ic_launcher_round.png`);
  } else {
    console.log(`❌ android/app/src/production/res/mipmap-${density}/ic_launcher_round.png`);
  }
});

// Check regular iOS icons (used by lower scheme)
console.log('\n📱 Verifying Regular iOS Icons (Lower Scheme):');
const iosRegularIconPath = path.join(__dirname, '..', 'ios', 'SubwayRNApp', 'Images.xcassets', 'AppIcon.appiconset');
const iosRegularIcons = [
  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'icon-1024.png'
];

iosRegularIcons.forEach(iconName => {
  const iconPath = path.join(iosRegularIconPath, iconName);
  if (fs.existsSync(iconPath)) {
    console.log(`✅ ios/SubwayRNApp/Images.xcassets/AppIcon.appiconset/${iconName}`);
  } else {
    console.log(`❌ ios/SubwayRNApp/Images.xcassets/AppIcon.appiconset/${iconName}`);
  }
});

// Check production iOS icons
console.log('\n📱 Verifying Production iOS Icons (Production Scheme):');
const iosProductionIconPath = path.join(__dirname, '..', 'ios', 'SubwayRNApp', 'Images.xcassets', 'AppIcon-Production.appiconset');
const iosProductionIcons = [
  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
  '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', 'icon-1024.png'
];

iosProductionIcons.forEach(iconName => {
  const iconPath = path.join(iosProductionIconPath, iconName);
  if (fs.existsSync(iconPath)) {
    console.log(`✅ ios/SubwayRNApp/Images.xcassets/AppIcon-Production.appiconset/${iconName}`);
  } else {
    console.log(`❌ ios/SubwayRNApp/Images.xcassets/AppIcon-Production.appiconset/${iconName}`);
  }
});

console.log('\n🎉 Icon Configuration Summary:');
console.log('===============================');
console.log('📱 Android:');
console.log('   • Lower flavor: Uses regular icons from main/res/');
console.log('   • Production flavor: Uses production icons from production/res/');
console.log('\n🍎 iOS:');
console.log('   • Lower scheme: Uses AppIcon asset (regular icons)');
console.log('   • Production scheme: Uses AppIcon-Production asset (production icons)');

console.log('\n🚀 Build Commands:');
console.log('• Lower (debug): npm run android:lower / npm run ios:lower');
console.log('• Production (debug): npm run android:production / npm run ios:production');
console.log('• Lower (release): npm run android:build-lower / npm run ios:build-lower');
console.log('• Production (release): npm run android:build-production / npm run ios:build-production');

console.log('\n💡 Note: Each build variant now has its own distinct app icon!');
