#!/usr/bin/env node

/**
 * Build Configuration Script
 * Sets up environment variables and configurations for different build targets
 */

const fs = require('fs');
const path = require('path');

const ENVIRONMENT = process.env.ENVIRONMENT || 'lower';

console.log(`🚇 Subway App Build Configuration`);
console.log(`Environment: ${ENVIRONMENT}`);

// Create a configuration file that can be imported
const configContent = `
/**
 * Auto-generated build configuration
 * DO NOT EDIT MANUALLY - Generated during build process
 */

export const BUILD_CONFIG = {
  ENVIRONMENT: '${ENVIRONMENT}',
  IS_PRODUCTION_BUILD: ${ENVIRONMENT === 'production'},
  TIMESTAMP: '${new Date().toISOString()}',
};
`;

const configPath = path.join(__dirname, '../src/config/BuildConfig.ts');
fs.writeFileSync(configPath, configContent);

console.log(`✅ Build configuration written to: ${configPath}`);

// Validate environment
if (!['lower', 'production'].includes(ENVIRONMENT)) {
  console.error(`❌ Invalid environment: ${ENVIRONMENT}`);
  console.log('Valid environments: lower, production');
  process.exit(1);
}

console.log(`🎯 Target Environment: ${ENVIRONMENT}`);
if (ENVIRONMENT === 'lower') {
  console.log('  - Environment switching enabled');
  console.log('  - Debug drawer enabled');
  console.log('  - Available environments: UAT, QE');
} else {
  console.log('  - Environment switching disabled');
  console.log('  - Debug drawer disabled');
  console.log('  - Production environment only');
}
