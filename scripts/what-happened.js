#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function readFileSafe(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (e) {
    return null;
  }
}

function printLatestSection(changelog) {
  const lines = changelog.split(/\r?\n/);
  const startIdx = lines.findIndex(l => l.trim().startsWith('## '));
  if (startIdx === -1) {
    console.log(changelog.trim());
    return;
  }
  // find next section start after startIdx
  let endIdx = lines.slice(startIdx + 1).findIndex(l => l.trim().startsWith('## '));
  if (endIdx !== -1) endIdx += startIdx + 1;
  const slice = endIdx === -1 ? lines.slice(startIdx) : lines.slice(startIdx, endIdx);
  console.log(slice.join('\n').trim());
}

(function main() {
  const root = path.resolve(__dirname, '..');
  const changelogPath = path.join(root, 'CHANGELOG.md');
  const changelog = readFileSafe(changelogPath);

  if (!changelog) {
    console.log('No CHANGELOG.md found. Summary of recent important changes:\n');
    console.log('- Added macOS 15 + React Native 0.80.x preflight compatibility guard for iOS scripts.');
    console.log('- New iOS convenience scripts: ios:launch, ios:sim, ios:open-sim, ios:boot:15pro, ios:launch:clean, ios:lower, ios:production.');
    console.log('- Podfile adjusted to avoid excluding arm64 for Simulator on Apple Silicon; added glog/gflags defs.');
    console.log('- README and TROUBLESHOOTING updated with guidance and override (OVERRIDE_MACOS15_RN80=1).');
    return;
  }

  const arg = process.argv[2] || '';
  if (arg === '--latest') {
    printLatestSection(changelog);
  } else {
    console.log(changelog.trim());
  }
})();
