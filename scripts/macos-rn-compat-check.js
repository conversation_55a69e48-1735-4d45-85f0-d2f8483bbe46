#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function getReactNativeVersion() {
  try {
    const pkgPath = path.resolve(__dirname, '..', 'package.json');
    const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
    const rn = (pkg.dependencies && pkg.dependencies['react-native']) || '';
    return String(rn).trim();
  } catch (e) {
    return '';
  }
}

function getMacOSVersion() {
  try {
    const out = execSync('sw_vers -productVersion', { stdio: ['ignore', 'pipe', 'ignore'] })
      .toString()
      .trim();
    return out; // e.g., 15.6.1
  } catch (e) {
    // Fallback: try to infer from Darwin kernel: macOS 15 => Darwin 24
    try {
      const uname = execSync('uname -r', { stdio: ['ignore', 'pipe', 'ignore'] })
        .toString()
        .trim();
      const major = parseInt(uname.split('.')[0], 10);
      // crude mapping
      const macMajor = major - 9; // Darwin 24 -> macOS 15
      return `${macMajor}.0.0`;
    } catch (_) {
      return '';
    }
  }
}

function semverMajor(version) {
  if (!version) return NaN;
  // strip leading operators like ^ ~
  const cleaned = version.replace(/^[^0-9]*/, '');
  const parts = cleaned.split('.');
  return parseInt(parts[0], 10);
}

function isRN80x(version) {
  const cleaned = String(version || '').trim();
  // Accept exact 0.80.x, including ranges like ^0.80.1
  const match = cleaned.match(/0\.80(\.|$)/);
  return !!match;
}

function isMacOS15OrNewer(v) {
  if (!v) return false;
  const major = parseInt(String(v).split('.')[0], 10);
  return !isNaN(major) && major >= 15;
}

(function main() {
  if (process.platform !== 'darwin') {
    process.exit(0);
  }

  if (process.env.OVERRIDE_MACOS15_RN80 === '1') {
    console.log('[compat-check] Override detected (OVERRIDE_MACOS15_RN80=1). Proceeding at your own risk.');
    process.exit(0);
  }

  const rnVersion = getReactNativeVersion();
  const macVersion = getMacOSVersion();

  if (isRN80x(rnVersion) && isMacOS15OrNewer(macVersion)) {
    const msg = `\n================= COMPATIBILITY BLOCKER =================\n` +
      `Detected environment: macOS ${macVersion} (Apple Silicon) + React Native ${rnVersion}.\n\n` +
      `This RN version (0.80.x) is known to be incompatible with macOS 15+ due to \n` +
      `a toolchain/gflags/patch issue. The app is unlikely to build/run reliably on iOS.\n\n` +
      `Recommended options:\n` +
      `  1) Upgrade React Native to 0.81+ or 0.82+\n` +
      `  2) Use a machine running macOS 14 or earlier\n` +
      `  3) Wait for upstream fixes or consider using Expo\n\n` +
      `To bypass this check temporarily (not recommended), re-run with:\n` +
      `  OVERRIDE_MACOS15_RN80=1 <your command>\n` +
      `=======================================================\n`;
    console.error(msg);
    process.exit(42); // non-zero to halt chained scripts
  }

  process.exit(0);
})();
