{"name": "SubwayRNApp", "version": "1.0.0", "private": true, "scripts": {"dev": "./scripts/dev.sh", "dev:android": "./scripts/dev.sh android", "dev:ios": "./scripts/dev.sh ios", "debug": "./scripts/debug.sh", "debug:android": "./scripts/debug.sh android", "debug:ios": "./scripts/debug.sh ios", "hermes:debug": "chrome://inspect", "android": "yarn start & sleep 5 && adb reverse tcp:8081 tcp:8081 && ANDROID_HOME=/Users/<USER>/Library/Android/sdk cd android && ./gradlew installLowerDebug && cd .. && adb shell am start -n com.subwayrnapp.lower/com.subwayrnapp.MainActivity -a android.intent.action.MAIN -c android.intent.category.LAUNCHER --ez debug true", "devtools": "npx @react-native/community-cli-plugin devtools", "android:lower": "yarn start & sleep 5 && ANDROID_HOME=/Users/<USER>/Library/Android/sdk cd android && ./gradlew installLowerDebug && cd .. && adb shell am start -n com.subwayrnapp.lower/com.subwayrnapp.MainActivity", "android:production": "yarn start & sleep 5 && ANDROID_HOME=/Users/<USER>/Library/Android/sdk cd android && ./gradlew installProductionDebug && cd .. && adb shell am start -n com.subwayrnapp/com.subwayrnapp.MainActivity", "android:lowerRelease": "cd android && ./gradlew app:assembleLowerRelease", "android:productionRelease": "cd android && ./gradlew app:assembleProductionRelease", "start:debug": "REACT_NATIVE_PACKAGER_PORT=8082 REACT_DEBUGGER='open -a \"React Native Debugger\"' react-native start --port 8082", "android:debug": "adb reverse tcp:8082 tcp:8082 && adb reverse tcp:8097 tcp:8097 && ANDROID_HOME=/Users/<USER>/Library/Android/sdk cd android && ./gradlew installLowerDebug && cd .. && adb shell am start -n com.subwayrnapp.lower/com.subwayrnapp.MainActivity -a android.intent.action.MAIN -c android.intent.category.LAUNCHER --ez debug true", "android:lower:debug": "yarn start:debug & sleep 5 && yarn android:debug", "debug:clear": "adb reverse --remove-all && watchman watch-del-all && yarn cache clean && REACT_NATIVE_PACKAGER_PORT=8082 react-native start --reset-cache --port 8082", "ios": "OVERRIDE_MACOS15_RN80=1 react-native run-ios", "ios:lower": "node scripts/macos-rn-compat-check.js && react-native run-ios --scheme SubwayRNApp-Lower", "ios:production": "node scripts/macos-rn-compat-check.js && react-native run-ios --scheme SubwayRNApp-Production", "ios:launch": "node scripts/macos-rn-compat-check.js && cd ios && pod install --repo-update && cd .. && react-native run-ios", "ios:sim": "node scripts/macos-rn-compat-check.js && react-native run-ios --simulator \"iPhone 15 Pro\"", "ios:open-sim": "open -a Simulator", "ios:boot:15pro": "xcrun simctl boot \"iPhone 15 Pro\" || true", "ios:launch:clean": "node scripts/macos-rn-compat-check.js && npx react-native start --reset-cache & sleep 2; cd ios && pod install --repo-update && cd .. && react-native run-ios --simulator \"iPhone 15 Pro\"", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/cli-platform-ios": "^20.0.1", "@react-native-firebase/analytics": "^20.1.0", "@react-native-firebase/app": "^20.1.0", "@react-native/new-app-screen": "0.81.1", "i18next": "^25.3.0", "lottie-react-native": "^7.2.3", "react": "19.1.0", "react-i18next": "^15.5.3", "react-native": "0.81.1", "react-native-device-info": "^14.0.4", "react-native-fast-image": "^8.6.3", "react-native-localize": "^3.4.2", "react-native-restart": "^0.0.27"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "latest", "@react-native/babel-preset": "0.81.1", "@react-native/community-cli-plugin": "0.81.1", "@react-native/eslint-config": "0.81.1", "@react-native/metro-config": "0.81.1", "@react-native/typescript-config": "0.81.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-restart": "^0.0.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}