# Changelog

All notable changes in this workspace during the current session.

## 2025-08-28

### iOS/macOS compatibility guard
- Added scripts/macos-rn-compat-check.js which blocks iOS build/run on macOS 15+ when using React Native 0.80.x and prints clear guidance.
- How to override (at your own risk): `OVERRIDE_MACOS15_RN80=1 yarn ios` (works with any iOS script).

### iOS run scripts quality-of-life
- New scripts in package.json:
  - `ios:launch` — install pods then run.
  - `ios:sim` — run on “iPhone 15 Pro”.
  - `ios:open-sim` — open the Simulator app.
  - `ios:boot:15pro` — boot “iPhone 15 Pro” device.
  - `ios:launch:clean` — reset Metro cache, install pods, run on “iPhone 15 Pro”.
  - Variants: `ios:lower`, `ios:production` use respective schemes.
- All iOS scripts call the macOS 15 + RN 0.80.x preflight guard first.

### CocoaPods / Xcode configuration
- Podfile no longer excludes arm64 for the iOS Simulator (required on Apple Silicon so the Simulator can run/install the app).
- Added defensive compiler definitions around glog/gflags for macOS 15+.

### Documentation updates
- README.md and TROUBLESHOOTING.md now document:
  - The Apple Silicon Simulator architecture note (don’t exclude arm64).
  - The macOS 15 + RN 0.80.x incompatibility warning and override variable.
  - The new iOS convenience scripts and how to use them.

### Quick reference
- See what changed at any time:
  - `yarn what-happened` or `npm run what-happened`
  - Or read this file: CHANGELOG.md
