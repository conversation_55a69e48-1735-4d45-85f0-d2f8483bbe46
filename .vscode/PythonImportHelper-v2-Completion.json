[{"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "pathlib", "kind": 6, "isExtraImport": true, "importPath": "pathlib", "description": "pathlib", "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "pytest", "kind": 6, "isExtraImport": true, "importPath": "pytest", "description": "pytest", "detail": "pytest", "documentation": {}}, {"label": "fixture", "importPath": "pytest", "description": "pytest", "isExtraImport": true, "detail": "pytest", "documentation": {}}, {"label": "spawn_web_server", "importPath": "pytest_pyodide.server", "description": "pytest_pyodide.server", "isExtraImport": true, "detail": "pytest_pyodide.server", "documentation": {}}, {"label": "runner", "importPath": "pytest_pyodide", "description": "pytest_pyodide", "isExtraImport": true, "detail": "pytest_pyodide", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "errno", "kind": 6, "isExtraImport": true, "importPath": "errno", "description": "errno", "detail": "errno", "documentation": {}}, {"label": "collections", "kind": 6, "isExtraImport": true, "importPath": "collections", "description": "collections", "detail": "collections", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "_Known", "kind": 6, "importPath": "node_modules.flatted.python.flatted", "description": "node_modules.flatted.python.flatted", "peekOfCode": "class _Known:\n    def __init__(self):\n        self.key = []\n        self.value = []\nclass _String:\n    def __init__(self, value):\n        self.value = value\ndef _array_keys(value):\n    keys = []\n    i = 0", "detail": "node_modules.flatted.python.flatted", "documentation": {}}, {"label": "_String", "kind": 6, "importPath": "node_modules.flatted.python.flatted", "description": "node_modules.flatted.python.flatted", "peekOfCode": "class _String:\n    def __init__(self, value):\n        self.value = value\ndef _array_keys(value):\n    keys = []\n    i = 0\n    for _ in value:\n        keys.append(i)\n        i += 1\n    return keys", "detail": "node_modules.flatted.python.flatted", "documentation": {}}, {"label": "parse", "kind": 2, "importPath": "node_modules.flatted.python.flatted", "description": "node_modules.flatted.python.flatted", "peekOfCode": "def parse(value, *args, **kwargs):\n    json = _json.loads(value, *args, **kwargs)\n    wrapped = []\n    for value in json:\n        wrapped.append(_wrap(value))\n    input = []\n    for value in wrapped:\n        if isinstance(value, _String):\n            input.append(value.value)\n        else:", "detail": "node_modules.flatted.python.flatted", "documentation": {}}, {"label": "stringify", "kind": 2, "importPath": "node_modules.flatted.python.flatted", "description": "node_modules.flatted.python.flatted", "peekOfCode": "def stringify(value, *args, **kwargs):\n    known = _Known()\n    input = []\n    output = []\n    i = int(_index(known, input, value))\n    while i < len(input):\n        output.append(_transform(known, input, input[i]))\n        i += 1\n    return _json.dumps(output, *args, **kwargs)", "detail": "node_modules.flatted.python.flatted", "documentation": {}}, {"label": "BaseRunner", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "class BaseRunner(runner._<PERSON><PERSON>erBaseRunner):\n    def __init__(\n        self,\n        *args,\n        test_dir,\n        **kwargs,\n    ):\n        self.test_dir = test_dir\n        super().__init__(\n            *args,", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "FirefoxRunner", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "class FirefoxRunner(<PERSON><PERSON><PERSON><PERSON>, runner.SeleniumFirefoxRunner):\n    pass\nclass ChromeRunner(<PERSON><PERSON><PERSON><PERSON>, runner.SeleniumChromeRunner):\n    pass\n# TODO: Figure out how to get <PERSON><PERSON><PERSON><PERSON><PERSON> to work.\nRUNNER_DICT = {x.browser: x for x in [FirefoxRunner, ChromeRunner]}\n@fixture(params=list(RUNNER_DICT), scope=\"class\")\ndef selenium_class_scope(request, web_server_main):\n    server_hostname, server_port, server_log = web_server_main\n    assert request.param in RUNNER_DICT", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "ChromeRunner", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "class ChromeRunner(<PERSON><PERSON><PERSON><PERSON>, runner.SeleniumChromeRunner):\n    pass\n# TODO: Figure out how to get <PERSON><PERSON><PERSON><PERSON><PERSON> to work.\nRUNNER_DICT = {x.browser: x for x in [FirefoxRunner, ChromeRunner]}\n@fixture(params=list(RUNNER_DICT), scope=\"class\")\ndef selenium_class_scope(request, web_server_main):\n    server_hostname, server_port, server_log = web_server_main\n    assert request.param in RUNNER_DICT\n    logger = logging.getLogger('selenium')\n    logger.setLevel(logging.DEBUG)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "selenium_class_scope", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "def selenium_class_scope(request, web_server_main):\n    server_hostname, server_port, server_log = web_server_main\n    assert request.param in RUNNER_DICT\n    logger = logging.getLogger('selenium')\n    logger.setLevel(logging.DEBUG)\n    cls = RUNNER_DICT[request.param]\n    selenium = cls(\n        test_dir=request.cls.TEST_BUILD_DIR,\n        server_port=server_port,\n        server_hostname=server_hostname,", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "selenium", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "def selenium(selenium_class_scope, request):\n    selenium = selenium_class_scope\n    request.cls.call_number += 1\n    # Refresh page every 50 calls to prevent firefox out of memory errors\n    if request.cls.call_number % 50 == 0:\n        selenium.driver.refresh()\n        selenium.javascript_setup()\n    selenium.clean_logs()\n    yield selenium\n@fixture(scope=\"session\")", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "web_server_main", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "def web_server_main(request):\n    with spawn_web_server(TEST_PATH) as output:\n        yield output", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "TEST_PATH", "kind": 5, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "TEST_PATH = Path(__file__).parents[1].resolve()\nclass BaseRunner(runner._<PERSON>rowserBaseRunner):\n    def __init__(\n        self,\n        *args,\n        test_dir,\n        **kwargs,\n    ):\n        self.test_dir = test_dir\n        super().__init__(", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "RUNNER_DICT", "kind": 5, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "peekOfCode": "RUNNER_DICT = {x.browser: x for x in [FirefoxRunner, ChromeRunner]}\n@fixture(params=list(RUNNER_DICT), scope=\"class\")\ndef selenium_class_scope(request, web_server_main):\n    server_hostname, server_port, server_log = web_server_main\n    assert request.param in RUNNER_DICT\n    logger = logging.getLogger('selenium')\n    logger.setLevel(logging.DEBUG)\n    cls = RUNNER_DICT[request.param]\n    selenium = cls(\n        test_dir=request.cls.TEST_BUILD_DIR,", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.conftest", "documentation": {}}, {"label": "TestCall", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "class TestCall:\n    TEST_BUILD_DIR = \"libffi.call.test\"\n    test_call = libffi_tests\nclass TestClosures:\n    TEST_BUILD_DIR = \"libffi.closures.test\"\n    test_closures = libffi_tests\ndef pytest_generate_tests(metafunc):\n    test_build_dir = metafunc.cls.TEST_BUILD_DIR\n    test_names = [x.stem for x in (TEST_PATH / test_build_dir).glob(\"*.o\")]\n    metafunc.parametrize(\"libffi_test\", test_names)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "TestClosures", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "class TestClosures:\n    TEST_BUILD_DIR = \"libffi.closures.test\"\n    test_closures = libffi_tests\ndef pytest_generate_tests(metafunc):\n    test_build_dir = metafunc.cls.TEST_BUILD_DIR\n    test_names = [x.stem for x in (TEST_PATH / test_build_dir).glob(\"*.o\")]\n    metafunc.parametrize(\"libffi_test\", test_names)\nif __name__ == \"__main__\":\n    subprocess.call([\"build-tests.sh\", \"libffi.call\"])", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "libffi_tests", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "def libffi_tests(self, selenium, libffi_test):\n    if libffi_test in xfails:\n        pytest.xfail(f'known failure with code \"{xfails[libffi_test]}\"')\n    res = selenium.run_js(\n        \"\"\"\n        window.TestModule = await <PERSON><PERSON><PERSON>();\n        \"\"\"\n    )\n    selenium.run_js(\n        f\"\"\"", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "pytest_generate_tests", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "def pytest_generate_tests(metafunc):\n    test_build_dir = metafunc.cls.TEST_BUILD_DIR\n    test_names = [x.stem for x in (TEST_PATH / test_build_dir).glob(\"*.o\")]\n    metafunc.parametrize(\"libffi_test\", test_names)\nif __name__ == \"__main__\":\n    subprocess.call([\"build-tests.sh\", \"libffi.call\"])", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "TEST_PATH", "kind": 5, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "TEST_PATH = pathlib.Path(__file__).parents[1].resolve()\nxfails = {}\ndef libffi_tests(self, selenium, libffi_test):\n    if libffi_test in xfails:\n        pytest.xfail(f'known failure with code \"{xfails[libffi_test]}\"')\n    res = selenium.run_js(\n        \"\"\"\n        window.TestModule = await Module();\n        \"\"\"\n    )", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "xfails", "kind": 5, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "peekOfCode": "xfails = {}\ndef libffi_tests(self, selenium, libffi_test):\n    if libffi_test in xfails:\n        pytest.xfail(f'known failure with code \"{xfails[libffi_test]}\"')\n    res = selenium.run_js(\n        \"\"\"\n        window.TestModule = await <PERSON><PERSON>le();\n        \"\"\"\n    )\n    selenium.run_js(", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.testsuite.emscripten.test_libffi", "documentation": {}}, {"label": "Platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class Platform(object):\n    pass\nclass i386_platform(Platform):\n    arch = 'i386'\n    prefix = \"#ifdef __i386__\\n\\n\"\n    suffix = \"\\n\\n#endif\"\n    src_dir = 'x86'\n    src_files = ['sysv.S', 'ffi.c', 'internal.h']\nclass x86_64_platform(Platform):\n    arch = 'x86_64'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "i386_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class i386_platform(Platform):\n    arch = 'i386'\n    prefix = \"#ifdef __i386__\\n\\n\"\n    suffix = \"\\n\\n#endif\"\n    src_dir = 'x86'\n    src_files = ['sysv.S', 'ffi.c', 'internal.h']\nclass x86_64_platform(Platform):\n    arch = 'x86_64'\n    prefix = \"#ifdef __x86_64__\\n\\n\"\n    suffix = \"\\n\\n#endif\"", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "x86_64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class x86_64_platform(Platform):\n    arch = 'x86_64'\n    prefix = \"#ifdef __x86_64__\\n\\n\"\n    suffix = \"\\n\\n#endif\"\n    src_dir = 'x86'\n    src_files = ['unix64.S', 'ffi64.c', 'ffiw64.c', 'win64.S', 'internal64.h', 'asmnames.h']\nclass arm64_platform(Platform):\n    arch = 'arm64'\n    prefix = \"#ifdef __arm64__\\n\\n\"\n    suffix = \"\\n\\n#endif\"", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class arm64_platform(Platform):\n    arch = 'arm64'\n    prefix = \"#ifdef __arm64__\\n\\n\"\n    suffix = \"\\n\\n#endif\"\n    src_dir = 'aarch64'\n    src_files = ['sysv.S', 'ffi.c', 'internal.h']\nclass armv7_platform(Platform):\n    arch = 'armv7'\n    prefix = \"#ifdef __arm__\\n\\n\"\n    suffix = \"\\n\\n#endif\"", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "armv7_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class armv7_platform(Platform):\n    arch = 'armv7'\n    prefix = \"#ifdef __arm__\\n\\n\"\n    suffix = \"\\n\\n#endif\"\n    src_dir = 'arm'\n    src_files = ['sysv.S', 'ffi.c', 'internal.h']\nclass ios_simulator_i386_platform(i386_platform):\n    target = 'i386-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "ios_simulator_i386_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class ios_simulator_i386_platform(i386_platform):\n    target = 'i386-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'\n    version_min = '-miphoneos-version-min=7.0'\nclass ios_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'\n    version_min = '-miphoneos-version-min=7.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "ios_simulator_x86_64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class ios_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'\n    version_min = '-miphoneos-version-min=7.0'\nclass ios_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'\n    version_min = '-miphoneos-version-min=7.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "ios_simulator_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class ios_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-ios-simulator'\n    directory = 'darwin_ios'\n    sdk = 'iphonesimulator'\n    version_min = '-miphoneos-version-min=7.0'\nclass ios_device_armv7_platform(armv7_platform):\n    target = 'armv7-apple-ios'\n    directory = 'darwin_ios'\n    sdk = 'iphoneos'\n    version_min = '-miphoneos-version-min=7.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "ios_device_armv7_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class ios_device_armv7_platform(armv7_platform):\n    target = 'armv7-apple-ios'\n    directory = 'darwin_ios'\n    sdk = 'iphoneos'\n    version_min = '-miphoneos-version-min=7.0'\nclass ios_device_arm64_platform(arm64_platform):\n    target = 'arm64-apple-ios'\n    directory = 'darwin_ios'\n    sdk = 'iphoneos'\n    version_min = '-miphoneos-version-min=7.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "ios_device_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class ios_device_arm64_platform(arm64_platform):\n    target = 'arm64-apple-ios'\n    directory = 'darwin_ios'\n    sdk = 'iphoneos'\n    version_min = '-miphoneos-version-min=7.0'\nclass desktop_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-macos'\n    directory = 'darwin_osx'\n    sdk = 'macosx'\n    version_min = '-mmacosx-version-min=10.6'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "desktop_x86_64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class desktop_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-macos'\n    directory = 'darwin_osx'\n    sdk = 'macosx'\n    version_min = '-mmacosx-version-min=10.6'\nclass desktop_arm64_platform(arm64_platform):\n    target = 'arm64-apple-macos'\n    directory = 'darwin_osx'\n    sdk = 'macosx'\n    version_min = '-mmacosx-version-min=11.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "desktop_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class desktop_arm64_platform(arm64_platform):\n    target = 'arm64-apple-macos'\n    directory = 'darwin_osx'\n    sdk = 'macosx'\n    version_min = '-mmacosx-version-min=11.0'\nclass tvos_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-tvos-simulator'\n    directory = 'darwin_tvos'\n    sdk = 'appletvsimulator'\n    version_min = '-mtvos-version-min=9.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "tvos_simulator_x86_64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class tvos_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-tvos-simulator'\n    directory = 'darwin_tvos'\n    sdk = 'appletvsimulator'\n    version_min = '-mtvos-version-min=9.0'\nclass tvos_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-tvos-simulator'\n    directory = 'darwin_tvos'\n    sdk = 'appletvsimulator'\n    version_min = '-mtvos-version-min=9.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "tvos_simulator_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class tvos_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-tvos-simulator'\n    directory = 'darwin_tvos'\n    sdk = 'appletvsimulator'\n    version_min = '-mtvos-version-min=9.0'\nclass tvos_device_arm64_platform(arm64_platform):\n    target = 'arm64-apple-tvos'\n    directory = 'darwin_tvos'\n    sdk = 'appletvos'\n    version_min = '-mtvos-version-min=9.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "tvos_device_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class tvos_device_arm64_platform(arm64_platform):\n    target = 'arm64-apple-tvos'\n    directory = 'darwin_tvos'\n    sdk = 'appletvos'\n    version_min = '-mtvos-version-min=9.0'\nclass watchos_simulator_i386_platform(i386_platform):\n    target = 'i386-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "watchos_simulator_i386_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class watchos_simulator_i386_platform(i386_platform):\n    target = 'i386-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'\nclass watchos_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "watchos_simulator_x86_64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class watchos_simulator_x86_64_platform(x86_64_platform):\n    target = 'x86_64-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'\nclass watchos_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "watchos_simulator_arm64_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class watchos_simulator_arm64_platform(arm64_platform):\n    target = 'arm64-apple-watchos-simulator'\n    directory = 'darwin_watchos'\n    sdk = 'watchsimulator'\n    version_min = '-mwatchos-version-min=4.0'\nclass watchos_device_armv7k_platform(armv7_platform):\n    target = 'armv7k-apple-watchos'\n    directory = 'darwin_watchos'\n    sdk = 'watchos'\n    arch = 'armv7k'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "watchos_device_armv7k_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class watchos_device_armv7k_platform(armv7_platform):\n    target = 'armv7k-apple-watchos'\n    directory = 'darwin_watchos'\n    sdk = 'watchos'\n    arch = 'armv7k'\n    version_min = '-mwatchos-version-min=4.0'\nclass watchos_device_arm64_32_platform(arm64_platform):\n    target = 'arm64_32-apple-watchos'\n    directory = 'darwin_watchos'\n    sdk = 'watchos'", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "watchos_device_arm64_32_platform", "kind": 6, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "class watchos_device_arm64_32_platform(arm64_platform):\n    target = 'arm64_32-apple-watchos'\n    directory = 'darwin_watchos'\n    sdk = 'watchos'\n    arch = 'arm64_32'\n    version_min = '-mwatchos-version-min=4.0'\ndef mkdir_p(path):\n    try:\n        os.makedirs(path)\n    except OSError as exc:  # Python >2.5", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "mkdir_p", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def mkdir_p(path):\n    try:\n        os.makedirs(path)\n    except OSError as exc:  # Python >2.5\n        if exc.errno != errno.EEXIST:\n            raise\ndef move_file(src_dir, dst_dir, filename, file_suffix=None, prefix='', suffix=''):\n    mkdir_p(dst_dir)\n    out_filename = filename\n    if file_suffix:", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "move_file", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def move_file(src_dir, dst_dir, filename, file_suffix=None, prefix='', suffix=''):\n    mkdir_p(dst_dir)\n    out_filename = filename\n    if file_suffix:\n        if filename in ['internal64.h', 'asmnames.h', 'internal.h']:\n            out_filename = filename\n        else:\n            split_name = os.path.splitext(filename)\n            out_filename = \"%s_%s%s\" % (split_name[0], file_suffix, split_name[1])\n    with open(os.path.join(src_dir, filename)) as in_file:", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "list_files", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def list_files(src_dir, pattern=None, filelist=None):\n    if pattern: filelist = glob.iglob(os.path.join(src_dir, pattern))\n    for file in filelist:\n        yield os.path.basename(file)\ndef copy_files(src_dir, dst_dir, pattern=None, filelist=None, file_suffix=None, prefix=None, suffix=None):\n    for filename in list_files(src_dir, pattern=pattern, filelist=filelist):\n        move_file(src_dir, dst_dir, filename, file_suffix=file_suffix, prefix=prefix, suffix=suffix)\ndef copy_src_platform_files(platform):\n    src_dir = os.path.join('src', platform.src_dir)\n    dst_dir = os.path.join(platform.directory, 'src', platform.src_dir)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "copy_files", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def copy_files(src_dir, dst_dir, pattern=None, filelist=None, file_suffix=None, prefix=None, suffix=None):\n    for filename in list_files(src_dir, pattern=pattern, filelist=filelist):\n        move_file(src_dir, dst_dir, filename, file_suffix=file_suffix, prefix=prefix, suffix=suffix)\ndef copy_src_platform_files(platform):\n    src_dir = os.path.join('src', platform.src_dir)\n    dst_dir = os.path.join(platform.directory, 'src', platform.src_dir)\n    copy_files(src_dir, dst_dir, filelist=platform.src_files, file_suffix=platform.arch, prefix=platform.prefix, suffix=platform.suffix)\ndef build_target(platform, platform_headers):\n    def xcrun_cmd(cmd):\n        return 'xcrun -sdk %s %s -target %s' % (platform.sdk, cmd, platform.target)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "copy_src_platform_files", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def copy_src_platform_files(platform):\n    src_dir = os.path.join('src', platform.src_dir)\n    dst_dir = os.path.join(platform.directory, 'src', platform.src_dir)\n    copy_files(src_dir, dst_dir, filelist=platform.src_files, file_suffix=platform.arch, prefix=platform.prefix, suffix=platform.suffix)\ndef build_target(platform, platform_headers):\n    def xcrun_cmd(cmd):\n        return 'xcrun -sdk %s %s -target %s' % (platform.sdk, cmd, platform.target)\n    tag='%s-%s' % (platform.sdk, platform.arch)\n    build_dir = 'build_%s' % tag\n    mkdir_p(build_dir)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "build_target", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def build_target(platform, platform_headers):\n    def xcrun_cmd(cmd):\n        return 'xcrun -sdk %s %s -target %s' % (platform.sdk, cmd, platform.target)\n    tag='%s-%s' % (platform.sdk, platform.arch)\n    build_dir = 'build_%s' % tag\n    mkdir_p(build_dir)\n    env = dict(CC=xcrun_cmd('clang'),\n               LD=xcrun_cmd('ld'),\n               CFLAGS='%s -fembed-bitcode' % (platform.version_min))\n    working_dir = os.getcwd()", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}, {"label": "generate_source_and_headers", "kind": 2, "importPath": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "description": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "peekOfCode": "def generate_source_and_headers(\n    generate_osx=True,\n    generate_ios=True,\n    generate_tvos=True,\n    generate_watchos=True,\n):\n    copy_files('src', 'darwin_common/src', pattern='*.c')\n    copy_files('include', 'darwin_common/include', pattern='*.h')\n    if generate_ios:\n        copy_src_platform_files(ios_simulator_i386_platform)", "detail": "vendor.bundle.ruby.2.6.0.gems.ffi-1.17.2.ext.ffi_c.libffi.generate-darwin-source-and-headers", "documentation": {}}]